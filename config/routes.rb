Rails.application.routes.draw do
  get 'calendar', to: "records#calendar"

  resources :daily_statistics do
    get :calculate_for_day, on: :collection
    get :calculate_all_dates, on: :collection
  end
  # resources :progresses
  resources :life_scenarios
  resources :projects do
    resources :progresses
  end
  resources :webpages
  resources :saved_searches
  resources :daily_checkings do
    get :create_today_checkings, on: :collection
    get :mark_done, on: :member
    get :mark_undone, on: :member
  end
  resources :daily_checking_templates
  resources :chat_messages
  resources :system_prompts

  resources :tags_records_relationships
  resources :tags do
    get :quick_assign, on: :collection
  end

  get "search", to: "dashboard#search", as: "search"
  get "dashboard", to: "dashboard#index", as: "dashboard"

  resources :ai_usages do
    get :all, on: :collection
  end
  resources :ai_models
  resources :ai_providers

  resources :attachments do
    get :ocr
    post :delete
  end

  devise_for :users, controllers: { registrations: "registrations" }
  resources :records do
    post :quick_create, on: :collection
    get :bulk_new, on: :collection
    post :bulk_create, on: :collection

    get :mark_done
    get :set_today
    get :set_to_date

    get :set_color

    get :reset_title

    get :clear_and_refetch_url

    post :bulk_set_tags, on: :collection

    get :todos, on: :collection
    get :todos_ai, on: :collection
    get :done_todos, on: :collection

    get :mark_pinned, on: :member
    get :mark_unpinned, on: :member
    get :mark_nice_to_have, on: :member
    get :mark_not_nice_to_have, on: :member
    get :mark_archived, on: :member
    get :mark_not_archived, on: :member
    get :mark_readed, on: :member
    get :mark_not_readed, on: :member

    get :atomy_follow_ups, on: :collection

    get :rows, on: :collection

    get :resources, on: :collection

    get :quotes, on: :collection

    # Different kinds
    get :people, on: :collection
    get :books, on: :collection
    get :journals, on: :collection

    # jobs
    get :jobs, on: :collection
    get :jobs_running, on: :collection
  end
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html



  # Defines the root path route ("/")
  root "records#index"
end
