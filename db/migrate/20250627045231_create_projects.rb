class CreateProjects < ActiveRecord::Migration[7.0]
  def change
    create_table :projects do |t|
      t.string :name
      t.text :objective
      t.date :begin_at
      t.date :target_end_at
      t.date :actual_end_at
      t.text :stakeholders
      t.text :schedules
      t.text :budgets
      t.text :risks
      t.text :issues
      t.boolean :is_pinned, default: false

      t.timestamps
    end
  end
end
