class CreateDailyStatistics < ActiveRecord::Migration[7.0]
  def change
    create_table :daily_statistics do |t|
      t.date :date_at
      t.integer :text_content_total_length
      t.integer :post_it_new_count
      t.integer :post_it_total
      t.integer :todo_new_count
      t.integer :todo_update_count
      t.integer :todo_total
      t.integer :follow_up_count
      t.integer :people_count
      t.integer :book_count
      t.integer :checking_count
      t.integer :atomy_new_count
      t.integer :atomy_total_count
      t.integer :records_new_count
      t.integer :records_total_count

      t.timestamps
    end
  end
end
