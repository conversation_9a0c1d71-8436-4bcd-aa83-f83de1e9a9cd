class CreateLifeScenarios < ActiveRecord::Migration[7.0]
  def change
    create_table :life_scenarios do |t|
      t.date :written_at
      t.text :preface
      t.string :title_1
      t.integer :score_1
      t.text :remark_1
      t.string :title_2
      t.integer :score_2
      t.text :remark_2
      t.string :title_3
      t.integer :score_3
      t.text :remark_3
      t.string :title_4
      t.integer :score_4
      t.text :remark_4
      t.string :title_5
      t.integer :score_5
      t.text :remark_5
      t.string :title_6
      t.integer :score_6
      t.text :remark_6
      t.string :title_7
      t.integer :score_7
      t.text :remark_7
      t.string :title_8
      t.integer :score_8
      t.text :remark_8
      t.text :postface

      t.timestamps
    end
  end
end
