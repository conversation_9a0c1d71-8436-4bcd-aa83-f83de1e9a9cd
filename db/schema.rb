# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_07_20_012428) do
  create_table "active_storage_attachments", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "ai_models", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.bigint "ai_provider_id", null: false
    t.string "name"
    t.string "slug"
    t.float "usd_per_million_input"
    t.float "usd_per_million_output"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "api_slug"
    t.index ["ai_provider_id"], name: "index_ai_models_on_ai_provider_id"
  end

  create_table "ai_providers", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "name"
    t.string "slug"
    t.string "api_base_url"
    t.string "api_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ai_usages", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.text "prompt"
    t.text "responsed_content"
    t.float "cost"
    t.string "provider"
    t.string "model"
    t.string "category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "chat_messages", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.bigint "record_id", null: false
    t.text "content", size: :long
    t.string "role"
    t.string "ai_model_slug"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_id"], name: "index_chat_messages_on_record_id"
  end

  create_table "daily_checking_templates", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "title"
    t.boolean "is_active", default: true
    t.text "remarks"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "rank", default: 0
    t.index ["rank"], name: "index_daily_checking_templates_on_rank"
  end

  create_table "daily_checkings", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.bigint "daily_checking_template_id", null: false
    t.date "date_at"
    t.boolean "is_checked", default: false
    t.text "remark"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "rank", default: 0
    t.index ["daily_checking_template_id"], name: "index_daily_checkings_on_daily_checking_template_id"
    t.index ["rank"], name: "index_daily_checkings_on_rank"
  end

  create_table "daily_statistics", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.date "date_at"
    t.integer "text_content_total_length"
    t.integer "post_it_new_count"
    t.integer "post_it_total"
    t.integer "todo_new_count"
    t.integer "todo_update_count"
    t.integer "todo_total"
    t.integer "follow_up_count"
    t.integer "people_count"
    t.integer "book_count"
    t.integer "checking_count"
    t.integer "atomy_new_count"
    t.integer "atomy_total_count"
    t.integer "records_new_count"
    t.integer "records_total_count"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "highlighted_text_total_length"
  end

  create_table "life_scenarios", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.date "written_at"
    t.text "preface"
    t.string "title_1"
    t.integer "score_1"
    t.text "remark_1"
    t.string "title_2"
    t.integer "score_2"
    t.text "remark_2"
    t.string "title_3"
    t.integer "score_3"
    t.text "remark_3"
    t.string "title_4"
    t.integer "score_4"
    t.text "remark_4"
    t.string "title_5"
    t.integer "score_5"
    t.text "remark_5"
    t.string "title_6"
    t.integer "score_6"
    t.text "remark_6"
    t.string "title_7"
    t.integer "score_7"
    t.text "remark_7"
    t.string "title_8"
    t.integer "score_8"
    t.text "remark_8"
    t.text "postface"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "progresses", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.bigint "project_id"
    t.integer "progress"
    t.text "remark"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "objective"
    t.text "stakeholders"
    t.text "schedules"
    t.text "budgets"
    t.text "risks"
    t.text "issues"
    t.index ["project_id"], name: "index_progresses_on_project_id"
  end

  create_table "projects", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "name"
    t.text "objective"
    t.date "begin_at"
    t.date "target_end_at"
    t.date "actual_end_at"
    t.text "stakeholders"
    t.text "schedules"
    t.text "budgets"
    t.text "risks"
    t.text "issues"
    t.boolean "is_pinned", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "progress"
    t.date "next_milestone_at"
    t.boolean "is_archived", default: false
    t.index ["is_archived"], name: "index_projects_on_is_archived"
  end

  create_table "prompts", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.text "system_prompt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "records", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "title"
    t.text "url"
    t.string "kind"
    t.text "text_content", size: :medium
    t.text "prompt", size: :medium
    t.text "generated_content", size: :medium
    t.text "source_content", size: :medium
    t.text "summary", size: :medium
    t.text "highlighted_text"
    t.text "frontmatters"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ai_model"
    t.integer "parent_id"
    t.integer "ai_process_status"
    t.string "tagss"
    t.date "date_at"
    t.bigint "system_prompt_id"
    t.string "color"
    t.boolean "is_pinned", default: false
    t.boolean "is_nice_to_have", default: false
    t.boolean "is_archived", default: false
    t.boolean "readed", default: false
    t.text "follow_up_remark"
    t.bigint "project_id"
    t.index ["ai_process_status"], name: "index_records_on_ai_process_status"
    t.index ["date_at"], name: "index_records_on_date_at"
    t.index ["is_archived"], name: "index_records_on_is_archived"
    t.index ["is_nice_to_have"], name: "index_records_on_is_nice_to_have"
    t.index ["is_pinned"], name: "index_records_on_is_pinned"
    t.index ["parent_id"], name: "index_records_on_parent_id"
    t.index ["project_id"], name: "index_records_on_project_id"
    t.index ["readed"], name: "index_records_on_readed"
    t.index ["system_prompt_id"], name: "index_records_on_system_prompt_id"
  end

  create_table "saved_searches", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "name"
    t.string "query"
    t.string "tags"
    t.string "search_for"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "system_prompts", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.text "system_prompt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tags", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "name"
    t.string "color"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "alias_of_tag_id"
  end

  create_table "tags_records_relationships", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.bigint "tag_id", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_id"], name: "index_tags_records_relationships_on_record_id"
    t.index ["tag_id"], name: "index_tags_records_relationships_on_tag_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "webpages", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.bigint "record_id"
    t.string "title"
    t.text "html", size: :long
    t.text "css", size: :long
    t.text "js", size: :long
    t.string "filename"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_id"], name: "index_webpages_on_record_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "ai_models", "ai_providers"
  add_foreign_key "chat_messages", "records"
  add_foreign_key "daily_checkings", "daily_checking_templates"
  add_foreign_key "progresses", "projects"
  add_foreign_key "records", "projects"
  add_foreign_key "tags_records_relationships", "records"
  add_foreign_key "tags_records_relationships", "tags"
  add_foreign_key "webpages", "records"
end
