version: '3.8'
services:
  db:
    image: mysql:8.0
    command: '--default-authentication-plugin=mysql_native_password'
    volumes:
      - mysql-data:/var/lib/mysql
      - ./mz-base-dev.sql:/docker-entrypoint-initdb.d/data.sql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: mz-base
    ports:
      - '3306:3306'

  web:
    build: .
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails s -p 3000 -b '0.0.0.0'"
    volumes:
      - .:/myapp
    ports:
      - "3000:3000"
    depends_on:
      - db
    environment:
      DATABASE_HOST: db
      DATABASE_USER: root
      DATABASE_PASSWORD: password
      RAILS_ENV: development

volumes:
  mysql-data: