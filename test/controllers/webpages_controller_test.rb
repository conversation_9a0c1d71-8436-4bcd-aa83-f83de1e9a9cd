require "test_helper"

class WebpagesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @webpage = webpages(:one)
  end

  test "should get index" do
    get webpages_url
    assert_response :success
  end

  test "should get new" do
    get new_webpage_url
    assert_response :success
  end

  test "should create webpage" do
    assert_difference("Webpage.count") do
      post webpages_url, params: { webpage: { css: @webpage.css, filename: @webpage.filename, html: @webpage.html, js: @webpage.js, record_id: @webpage.record_id, title: @webpage.title } }
    end

    assert_redirected_to webpage_url(Webpage.last)
  end

  test "should show webpage" do
    get webpage_url(@webpage)
    assert_response :success
  end

  test "should get edit" do
    get edit_webpage_url(@webpage)
    assert_response :success
  end

  test "should update webpage" do
    patch webpage_url(@webpage), params: { webpage: { css: @webpage.css, filename: @webpage.filename, html: @webpage.html, js: @webpage.js, record_id: @webpage.record_id, title: @webpage.title } }
    assert_redirected_to webpage_url(@webpage)
  end

  test "should destroy webpage" do
    assert_difference("Webpage.count", -1) do
      delete webpage_url(@webpage)
    end

    assert_redirected_to webpages_url
  end
end
