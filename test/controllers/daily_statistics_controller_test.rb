require "test_helper"

class DailyStatisticsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @daily_statistic = daily_statistics(:one)
  end

  test "should get index" do
    get daily_statistics_url
    assert_response :success
  end

  test "should get new" do
    get new_daily_statistic_url
    assert_response :success
  end

  test "should create daily_statistic" do
    assert_difference("DailyStatistic.count") do
      post daily_statistics_url, params: { daily_statistic: { atomy_new_count: @daily_statistic.atomy_new_count, atomy_total_count: @daily_statistic.atomy_total_count, book_count: @daily_statistic.book_count, checking_count: @daily_statistic.checking_count, date_at: @daily_statistic.date_at, follow_up_count: @daily_statistic.follow_up_count, people_count: @daily_statistic.people_count, post_it_new_count: @daily_statistic.post_it_new_count, post_it_total: @daily_statistic.post_it_total, records_new_count: @daily_statistic.records_new_count, records_total_count: @daily_statistic.records_total_count, text_content_total_length: @daily_statistic.text_content_total_length, todo_new_count: @daily_statistic.todo_new_count, todo_total: @daily_statistic.todo_total, todo_update_count: @daily_statistic.todo_update_count } }
    end

    assert_redirected_to daily_statistic_url(DailyStatistic.last)
  end

  test "should show daily_statistic" do
    get daily_statistic_url(@daily_statistic)
    assert_response :success
  end

  test "should get edit" do
    get edit_daily_statistic_url(@daily_statistic)
    assert_response :success
  end

  test "should update daily_statistic" do
    patch daily_statistic_url(@daily_statistic), params: { daily_statistic: { atomy_new_count: @daily_statistic.atomy_new_count, atomy_total_count: @daily_statistic.atomy_total_count, book_count: @daily_statistic.book_count, checking_count: @daily_statistic.checking_count, date_at: @daily_statistic.date_at, follow_up_count: @daily_statistic.follow_up_count, people_count: @daily_statistic.people_count, post_it_new_count: @daily_statistic.post_it_new_count, post_it_total: @daily_statistic.post_it_total, records_new_count: @daily_statistic.records_new_count, records_total_count: @daily_statistic.records_total_count, text_content_total_length: @daily_statistic.text_content_total_length, todo_new_count: @daily_statistic.todo_new_count, todo_total: @daily_statistic.todo_total, todo_update_count: @daily_statistic.todo_update_count } }
    assert_redirected_to daily_statistic_url(@daily_statistic)
  end

  test "should destroy daily_statistic" do
    assert_difference("DailyStatistic.count", -1) do
      delete daily_statistic_url(@daily_statistic)
    end

    assert_redirected_to daily_statistics_url
  end
end
