require "test_helper"

class DailyCheckingsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @daily_checking = daily_checkings(:one)
  end

  test "should get index" do
    get daily_checkings_url
    assert_response :success
  end

  test "should get new" do
    get new_daily_checking_url
    assert_response :success
  end

  test "should create daily_checking" do
    assert_difference("DailyChecking.count") do
      post daily_checkings_url, params: { daily_checking: { daily_checking_template_id: @daily_checking.daily_checking_template_id, date_at: @daily_checking.date_at, is_checked: @daily_checking.is_checked, remark: @daily_checking.remark } }
    end

    assert_redirected_to daily_checking_url(DailyChecking.last)
  end

  test "should show daily_checking" do
    get daily_checking_url(@daily_checking)
    assert_response :success
  end

  test "should get edit" do
    get edit_daily_checking_url(@daily_checking)
    assert_response :success
  end

  test "should update daily_checking" do
    patch daily_checking_url(@daily_checking), params: { daily_checking: { daily_checking_template_id: @daily_checking.daily_checking_template_id, date_at: @daily_checking.date_at, is_checked: @daily_checking.is_checked, remark: @daily_checking.remark } }
    assert_redirected_to daily_checking_url(@daily_checking)
  end

  test "should destroy daily_checking" do
    assert_difference("DailyChecking.count", -1) do
      delete daily_checking_url(@daily_checking)
    end

    assert_redirected_to daily_checkings_url
  end
end
