require "test_helper"

class ProjectsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @project = projects(:one)
  end

  test "should get index" do
    get projects_url
    assert_response :success
  end

  test "should get new" do
    get new_project_url
    assert_response :success
  end

  test "should create project" do
    assert_difference("Project.count") do
      post projects_url, params: { project: { actual_end_at: @project.actual_end_at, begin_at: @project.begin_at, budgets: @project.budgets, is_pinned: @project.is_pinned, issues: @project.issues, name: @project.name, objective: @project.objective, risks: @project.risks, schedules: @project.schedules, stakeholders: @project.stakeholders, target_end_at: @project.target_end_at } }
    end

    assert_redirected_to project_url(Project.last)
  end

  test "should show project" do
    get project_url(@project)
    assert_response :success
  end

  test "should get edit" do
    get edit_project_url(@project)
    assert_response :success
  end

  test "should update project" do
    patch project_url(@project), params: { project: { actual_end_at: @project.actual_end_at, begin_at: @project.begin_at, budgets: @project.budgets, is_pinned: @project.is_pinned, issues: @project.issues, name: @project.name, objective: @project.objective, risks: @project.risks, schedules: @project.schedules, stakeholders: @project.stakeholders, target_end_at: @project.target_end_at } }
    assert_redirected_to project_url(@project)
  end

  test "should destroy project" do
    assert_difference("Project.count", -1) do
      delete project_url(@project)
    end

    assert_redirected_to projects_url
  end
end
