require "test_helper"

class TagsRecordsRelationshipsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @tags_records_relationship = tags_records_relationships(:one)
  end

  test "should get index" do
    get tags_records_relationships_url
    assert_response :success
  end

  test "should get new" do
    get new_tags_records_relationship_url
    assert_response :success
  end

  test "should create tags_records_relationship" do
    assert_difference("TagsRecordsRelationship.count") do
      post tags_records_relationships_url, params: { tags_records_relationship: { record_id: @tags_records_relationship.record_id, tag_id: @tags_records_relationship.tag_id } }
    end

    assert_redirected_to tags_records_relationship_url(TagsRecordsRelationship.last)
  end

  test "should show tags_records_relationship" do
    get tags_records_relationship_url(@tags_records_relationship)
    assert_response :success
  end

  test "should get edit" do
    get edit_tags_records_relationship_url(@tags_records_relationship)
    assert_response :success
  end

  test "should update tags_records_relationship" do
    patch tags_records_relationship_url(@tags_records_relationship), params: { tags_records_relationship: { record_id: @tags_records_relationship.record_id, tag_id: @tags_records_relationship.tag_id } }
    assert_redirected_to tags_records_relationship_url(@tags_records_relationship)
  end

  test "should destroy tags_records_relationship" do
    assert_difference("TagsRecordsRelationship.count", -1) do
      delete tags_records_relationship_url(@tags_records_relationship)
    end

    assert_redirected_to tags_records_relationships_url
  end
end
