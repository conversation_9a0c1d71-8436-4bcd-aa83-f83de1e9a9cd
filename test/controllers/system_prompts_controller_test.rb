require "test_helper"

class SystemPromptsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @system_prompt = system_prompts(:one)
  end

  test "should get index" do
    get system_prompts_url
    assert_response :success
  end

  test "should get new" do
    get new_system_prompt_url
    assert_response :success
  end

  test "should create system_prompt" do
    assert_difference("SystemPrompt.count") do
      post system_prompts_url, params: { system_prompt: { system_prompt: @system_prompt.system_prompt } }
    end

    assert_redirected_to system_prompt_url(SystemPrompt.last)
  end

  test "should show system_prompt" do
    get system_prompt_url(@system_prompt)
    assert_response :success
  end

  test "should get edit" do
    get edit_system_prompt_url(@system_prompt)
    assert_response :success
  end

  test "should update system_prompt" do
    patch system_prompt_url(@system_prompt), params: { system_prompt: { system_prompt: @system_prompt.system_prompt } }
    assert_redirected_to system_prompt_url(@system_prompt)
  end

  test "should destroy system_prompt" do
    assert_difference("SystemPrompt.count", -1) do
      delete system_prompt_url(@system_prompt)
    end

    assert_redirected_to system_prompts_url
  end
end
