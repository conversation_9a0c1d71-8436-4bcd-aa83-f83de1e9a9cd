require "test_helper"

class DailyCheckingTemplatesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @daily_checking_template = daily_checking_templates(:one)
  end

  test "should get index" do
    get daily_checking_templates_url
    assert_response :success
  end

  test "should get new" do
    get new_daily_checking_template_url
    assert_response :success
  end

  test "should create daily_checking_template" do
    assert_difference("DailyCheckingTemplate.count") do
      post daily_checking_templates_url, params: { daily_checking_template: { is_active: @daily_checking_template.is_active, remarks: @daily_checking_template.remarks, title: @daily_checking_template.title } }
    end

    assert_redirected_to daily_checking_template_url(DailyCheckingTemplate.last)
  end

  test "should show daily_checking_template" do
    get daily_checking_template_url(@daily_checking_template)
    assert_response :success
  end

  test "should get edit" do
    get edit_daily_checking_template_url(@daily_checking_template)
    assert_response :success
  end

  test "should update daily_checking_template" do
    patch daily_checking_template_url(@daily_checking_template), params: { daily_checking_template: { is_active: @daily_checking_template.is_active, remarks: @daily_checking_template.remarks, title: @daily_checking_template.title } }
    assert_redirected_to daily_checking_template_url(@daily_checking_template)
  end

  test "should destroy daily_checking_template" do
    assert_difference("DailyCheckingTemplate.count", -1) do
      delete daily_checking_template_url(@daily_checking_template)
    end

    assert_redirected_to daily_checking_templates_url
  end
end
