require "test_helper"

class LifeScenariosControllerTest < ActionDispatch::IntegrationTest
  setup do
    @life_scenario = life_scenarios(:one)
  end

  test "should get index" do
    get life_scenarios_url
    assert_response :success
  end

  test "should get new" do
    get new_life_scenario_url
    assert_response :success
  end

  test "should create life_scenario" do
    assert_difference("LifeScenario.count") do
      post life_scenarios_url, params: { life_scenario: { postface: @life_scenario.postface, preface: @life_scenario.preface, remark_1: @life_scenario.remark_1, remark_2: @life_scenario.remark_2, remark_3: @life_scenario.remark_3, remark_4: @life_scenario.remark_4, remark_5: @life_scenario.remark_5, remark_6: @life_scenario.remark_6, remark_7: @life_scenario.remark_7, remark_8: @life_scenario.remark_8, score_1: @life_scenario.score_1, score_2: @life_scenario.score_2, score_3: @life_scenario.score_3, score_4: @life_scenario.score_4, score_5: @life_scenario.score_5, score_6: @life_scenario.score_6, score_7: @life_scenario.score_7, score_8: @life_scenario.score_8, title_1: @life_scenario.title_1, title_2: @life_scenario.title_2, title_3: @life_scenario.title_3, title_4: @life_scenario.title_4, title_5: @life_scenario.title_5, title_6: @life_scenario.title_6, title_7: @life_scenario.title_7, title_8: @life_scenario.title_8, written_at: @life_scenario.written_at } }
    end

    assert_redirected_to life_scenario_url(LifeScenario.last)
  end

  test "should show life_scenario" do
    get life_scenario_url(@life_scenario)
    assert_response :success
  end

  test "should get edit" do
    get edit_life_scenario_url(@life_scenario)
    assert_response :success
  end

  test "should update life_scenario" do
    patch life_scenario_url(@life_scenario), params: { life_scenario: { postface: @life_scenario.postface, preface: @life_scenario.preface, remark_1: @life_scenario.remark_1, remark_2: @life_scenario.remark_2, remark_3: @life_scenario.remark_3, remark_4: @life_scenario.remark_4, remark_5: @life_scenario.remark_5, remark_6: @life_scenario.remark_6, remark_7: @life_scenario.remark_7, remark_8: @life_scenario.remark_8, score_1: @life_scenario.score_1, score_2: @life_scenario.score_2, score_3: @life_scenario.score_3, score_4: @life_scenario.score_4, score_5: @life_scenario.score_5, score_6: @life_scenario.score_6, score_7: @life_scenario.score_7, score_8: @life_scenario.score_8, title_1: @life_scenario.title_1, title_2: @life_scenario.title_2, title_3: @life_scenario.title_3, title_4: @life_scenario.title_4, title_5: @life_scenario.title_5, title_6: @life_scenario.title_6, title_7: @life_scenario.title_7, title_8: @life_scenario.title_8, written_at: @life_scenario.written_at } }
    assert_redirected_to life_scenario_url(@life_scenario)
  end

  test "should destroy life_scenario" do
    assert_difference("LifeScenario.count", -1) do
      delete life_scenario_url(@life_scenario)
    end

    assert_redirected_to life_scenarios_url
  end
end
