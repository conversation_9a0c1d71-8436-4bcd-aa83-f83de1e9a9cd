require "test_helper"

class AiProvidersControllerTest < ActionDispatch::IntegrationTest
  setup do
    @ai_provider = ai_providers(:one)
  end

  test "should get index" do
    get ai_providers_url
    assert_response :success
  end

  test "should get new" do
    get new_ai_provider_url
    assert_response :success
  end

  test "should create ai_provider" do
    assert_difference("AiProvider.count") do
      post ai_providers_url, params: { ai_provider: { api_base_url: @ai_provider.api_base_url, api_key: @ai_provider.api_key, name: @ai_provider.name, slug: @ai_provider.slug } }
    end

    assert_redirected_to ai_provider_url(AiProvider.last)
  end

  test "should show ai_provider" do
    get ai_provider_url(@ai_provider)
    assert_response :success
  end

  test "should get edit" do
    get edit_ai_provider_url(@ai_provider)
    assert_response :success
  end

  test "should update ai_provider" do
    patch ai_provider_url(@ai_provider), params: { ai_provider: { api_base_url: @ai_provider.api_base_url, api_key: @ai_provider.api_key, name: @ai_provider.name, slug: @ai_provider.slug } }
    assert_redirected_to ai_provider_url(@ai_provider)
  end

  test "should destroy ai_provider" do
    assert_difference("AiProvider.count", -1) do
      delete ai_provider_url(@ai_provider)
    end

    assert_redirected_to ai_providers_url
  end
end
