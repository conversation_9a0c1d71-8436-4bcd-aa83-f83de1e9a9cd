require "test_helper"

class AiUsagesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @ai_usage = ai_usages(:one)
  end

  test "should get index" do
    get ai_usages_url
    assert_response :success
  end

  test "should get new" do
    get new_ai_usage_url
    assert_response :success
  end

  test "should create ai_usage" do
    assert_difference("AiUsage.count") do
      post ai_usages_url, params: { ai_usage: { category: @ai_usage.category, cost: @ai_usage.cost, model: @ai_usage.model, prompt: @ai_usage.prompt, provider: @ai_usage.provider, responsed_content: @ai_usage.responsed_content } }
    end

    assert_redirected_to ai_usage_url(AiUsage.last)
  end

  test "should show ai_usage" do
    get ai_usage_url(@ai_usage)
    assert_response :success
  end

  test "should get edit" do
    get edit_ai_usage_url(@ai_usage)
    assert_response :success
  end

  test "should update ai_usage" do
    patch ai_usage_url(@ai_usage), params: { ai_usage: { category: @ai_usage.category, cost: @ai_usage.cost, model: @ai_usage.model, prompt: @ai_usage.prompt, provider: @ai_usage.provider, responsed_content: @ai_usage.responsed_content } }
    assert_redirected_to ai_usage_url(@ai_usage)
  end

  test "should destroy ai_usage" do
    assert_difference("AiUsage.count", -1) do
      delete ai_usage_url(@ai_usage)
    end

    assert_redirected_to ai_usages_url
  end
end
