require "application_system_test_case"

class AiProvidersTest < ApplicationSystemTestCase
  setup do
    @ai_provider = ai_providers(:one)
  end

  test "visiting the index" do
    visit ai_providers_url
    assert_selector "h1", text: "Ai providers"
  end

  test "should create ai provider" do
    visit ai_providers_url
    click_on "New ai provider"

    fill_in "Api base url", with: @ai_provider.api_base_url
    fill_in "Api key", with: @ai_provider.api_key
    fill_in "Name", with: @ai_provider.name
    fill_in "Slug", with: @ai_provider.slug
    click_on "Create Ai provider"

    assert_text "Ai provider was successfully created"
    click_on "Back"
  end

  test "should update Ai provider" do
    visit ai_provider_url(@ai_provider)
    click_on "Edit this ai provider", match: :first

    fill_in "Api base url", with: @ai_provider.api_base_url
    fill_in "Api key", with: @ai_provider.api_key
    fill_in "Name", with: @ai_provider.name
    fill_in "Slug", with: @ai_provider.slug
    click_on "Update Ai provider"

    assert_text "Ai provider was successfully updated"
    click_on "Back"
  end

  test "should destroy Ai provider" do
    visit ai_provider_url(@ai_provider)
    click_on "Destroy this ai provider", match: :first

    assert_text "Ai provider was successfully destroyed"
  end
end
