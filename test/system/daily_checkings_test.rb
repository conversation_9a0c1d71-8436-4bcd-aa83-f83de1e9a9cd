require "application_system_test_case"

class DailyCheckingsTest < ApplicationSystemTestCase
  setup do
    @daily_checking = daily_checkings(:one)
  end

  test "visiting the index" do
    visit daily_checkings_url
    assert_selector "h1", text: "Daily checkings"
  end

  test "should create daily checking" do
    visit daily_checkings_url
    click_on "New daily checking"

    fill_in "Daily checking template", with: @daily_checking.daily_checking_template_id
    fill_in "Date at", with: @daily_checking.date_at
    check "Is checked" if @daily_checking.is_checked
    fill_in "Remark", with: @daily_checking.remark
    click_on "Create Daily checking"

    assert_text "Daily checking was successfully created"
    click_on "Back"
  end

  test "should update Daily checking" do
    visit daily_checking_url(@daily_checking)
    click_on "Edit this daily checking", match: :first

    fill_in "Daily checking template", with: @daily_checking.daily_checking_template_id
    fill_in "Date at", with: @daily_checking.date_at
    check "Is checked" if @daily_checking.is_checked
    fill_in "Remark", with: @daily_checking.remark
    click_on "Update Daily checking"

    assert_text "Daily checking was successfully updated"
    click_on "Back"
  end

  test "should destroy Daily checking" do
    visit daily_checking_url(@daily_checking)
    click_on "Destroy this daily checking", match: :first

    assert_text "Daily checking was successfully destroyed"
  end
end
