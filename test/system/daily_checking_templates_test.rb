require "application_system_test_case"

class DailyCheckingTemplatesTest < ApplicationSystemTestCase
  setup do
    @daily_checking_template = daily_checking_templates(:one)
  end

  test "visiting the index" do
    visit daily_checking_templates_url
    assert_selector "h1", text: "Daily checking templates"
  end

  test "should create daily checking template" do
    visit daily_checking_templates_url
    click_on "New daily checking template"

    check "Is active" if @daily_checking_template.is_active
    fill_in "Remarks", with: @daily_checking_template.remarks
    fill_in "Title", with: @daily_checking_template.title
    click_on "Create Daily checking template"

    assert_text "Daily checking template was successfully created"
    click_on "Back"
  end

  test "should update Daily checking template" do
    visit daily_checking_template_url(@daily_checking_template)
    click_on "Edit this daily checking template", match: :first

    check "Is active" if @daily_checking_template.is_active
    fill_in "Remarks", with: @daily_checking_template.remarks
    fill_in "Title", with: @daily_checking_template.title
    click_on "Update Daily checking template"

    assert_text "Daily checking template was successfully updated"
    click_on "Back"
  end

  test "should destroy Daily checking template" do
    visit daily_checking_template_url(@daily_checking_template)
    click_on "Destroy this daily checking template", match: :first

    assert_text "Daily checking template was successfully destroyed"
  end
end
