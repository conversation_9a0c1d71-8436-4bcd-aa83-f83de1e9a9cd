require "application_system_test_case"

class WebpagesTest < ApplicationSystemTestCase
  setup do
    @webpage = webpages(:one)
  end

  test "visiting the index" do
    visit webpages_url
    assert_selector "h1", text: "Webpages"
  end

  test "should create webpage" do
    visit webpages_url
    click_on "New webpage"

    fill_in "Css", with: @webpage.css
    fill_in "Filename", with: @webpage.filename
    fill_in "Html", with: @webpage.html
    fill_in "Js", with: @webpage.js
    fill_in "Record", with: @webpage.record_id
    fill_in "Title", with: @webpage.title
    click_on "Create Webpage"

    assert_text "Webpage was successfully created"
    click_on "Back"
  end

  test "should update Webpage" do
    visit webpage_url(@webpage)
    click_on "Edit this webpage", match: :first

    fill_in "Css", with: @webpage.css
    fill_in "Filename", with: @webpage.filename
    fill_in "Html", with: @webpage.html
    fill_in "Js", with: @webpage.js
    fill_in "Record", with: @webpage.record_id
    fill_in "Title", with: @webpage.title
    click_on "Update Webpage"

    assert_text "Webpage was successfully updated"
    click_on "Back"
  end

  test "should destroy Webpage" do
    visit webpage_url(@webpage)
    click_on "Destroy this webpage", match: :first

    assert_text "Webpage was successfully destroyed"
  end
end
