require "application_system_test_case"

class DailyStatisticsTest < ApplicationSystemTestCase
  setup do
    @daily_statistic = daily_statistics(:one)
  end

  test "visiting the index" do
    visit daily_statistics_url
    assert_selector "h1", text: "Daily statistics"
  end

  test "should create daily statistic" do
    visit daily_statistics_url
    click_on "New daily statistic"

    fill_in "Atomy new count", with: @daily_statistic.atomy_new_count
    fill_in "Atomy total count", with: @daily_statistic.atomy_total_count
    fill_in "Book count", with: @daily_statistic.book_count
    fill_in "Checking count", with: @daily_statistic.checking_count
    fill_in "Date at", with: @daily_statistic.date_at
    fill_in "Follow up count", with: @daily_statistic.follow_up_count
    fill_in "People count", with: @daily_statistic.people_count
    fill_in "Post it new count", with: @daily_statistic.post_it_new_count
    fill_in "Post it total", with: @daily_statistic.post_it_total
    fill_in "Records new count", with: @daily_statistic.records_new_count
    fill_in "Records total count", with: @daily_statistic.records_total_count
    fill_in "Text content total length", with: @daily_statistic.text_content_total_length
    fill_in "Todo new count", with: @daily_statistic.todo_new_count
    fill_in "Todo total", with: @daily_statistic.todo_total
    fill_in "Todo update count", with: @daily_statistic.todo_update_count
    click_on "Create Daily statistic"

    assert_text "Daily statistic was successfully created"
    click_on "Back"
  end

  test "should update Daily statistic" do
    visit daily_statistic_url(@daily_statistic)
    click_on "Edit this daily statistic", match: :first

    fill_in "Atomy new count", with: @daily_statistic.atomy_new_count
    fill_in "Atomy total count", with: @daily_statistic.atomy_total_count
    fill_in "Book count", with: @daily_statistic.book_count
    fill_in "Checking count", with: @daily_statistic.checking_count
    fill_in "Date at", with: @daily_statistic.date_at
    fill_in "Follow up count", with: @daily_statistic.follow_up_count
    fill_in "People count", with: @daily_statistic.people_count
    fill_in "Post it new count", with: @daily_statistic.post_it_new_count
    fill_in "Post it total", with: @daily_statistic.post_it_total
    fill_in "Records new count", with: @daily_statistic.records_new_count
    fill_in "Records total count", with: @daily_statistic.records_total_count
    fill_in "Text content total length", with: @daily_statistic.text_content_total_length
    fill_in "Todo new count", with: @daily_statistic.todo_new_count
    fill_in "Todo total", with: @daily_statistic.todo_total
    fill_in "Todo update count", with: @daily_statistic.todo_update_count
    click_on "Update Daily statistic"

    assert_text "Daily statistic was successfully updated"
    click_on "Back"
  end

  test "should destroy Daily statistic" do
    visit daily_statistic_url(@daily_statistic)
    click_on "Destroy this daily statistic", match: :first

    assert_text "Daily statistic was successfully destroyed"
  end
end
