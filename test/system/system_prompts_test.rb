require "application_system_test_case"

class SystemPromptsTest < ApplicationSystemTestCase
  setup do
    @system_prompt = system_prompts(:one)
  end

  test "visiting the index" do
    visit system_prompts_url
    assert_selector "h1", text: "System prompts"
  end

  test "should create system prompt" do
    visit system_prompts_url
    click_on "New system prompt"

    fill_in "System prompt", with: @system_prompt.system_prompt
    click_on "Create System prompt"

    assert_text "System prompt was successfully created"
    click_on "Back"
  end

  test "should update System prompt" do
    visit system_prompt_url(@system_prompt)
    click_on "Edit this system prompt", match: :first

    fill_in "System prompt", with: @system_prompt.system_prompt
    click_on "Update System prompt"

    assert_text "System prompt was successfully updated"
    click_on "Back"
  end

  test "should destroy System prompt" do
    visit system_prompt_url(@system_prompt)
    click_on "Destroy this system prompt", match: :first

    assert_text "System prompt was successfully destroyed"
  end
end
