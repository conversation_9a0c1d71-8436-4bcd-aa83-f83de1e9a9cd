require "application_system_test_case"

class TagsRecordsRelationshipsTest < ApplicationSystemTestCase
  setup do
    @tags_records_relationship = tags_records_relationships(:one)
  end

  test "visiting the index" do
    visit tags_records_relationships_url
    assert_selector "h1", text: "Tags records relationships"
  end

  test "should create tags records relationship" do
    visit tags_records_relationships_url
    click_on "New tags records relationship"

    fill_in "Record", with: @tags_records_relationship.record_id
    fill_in "Tag", with: @tags_records_relationship.tag_id
    click_on "Create Tags records relationship"

    assert_text "Tags records relationship was successfully created"
    click_on "Back"
  end

  test "should update Tags records relationship" do
    visit tags_records_relationship_url(@tags_records_relationship)
    click_on "Edit this tags records relationship", match: :first

    fill_in "Record", with: @tags_records_relationship.record_id
    fill_in "Tag", with: @tags_records_relationship.tag_id
    click_on "Update Tags records relationship"

    assert_text "Tags records relationship was successfully updated"
    click_on "Back"
  end

  test "should destroy Tags records relationship" do
    visit tags_records_relationship_url(@tags_records_relationship)
    click_on "Destroy this tags records relationship", match: :first

    assert_text "Tags records relationship was successfully destroyed"
  end
end
