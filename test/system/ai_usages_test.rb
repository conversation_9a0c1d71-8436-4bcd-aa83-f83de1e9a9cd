require "application_system_test_case"

class AiUsagesTest < ApplicationSystemTestCase
  setup do
    @ai_usage = ai_usages(:one)
  end

  test "visiting the index" do
    visit ai_usages_url
    assert_selector "h1", text: "Ai usages"
  end

  test "should create ai usage" do
    visit ai_usages_url
    click_on "New ai usage"

    fill_in "Category", with: @ai_usage.category
    fill_in "Cost", with: @ai_usage.cost
    fill_in "Model", with: @ai_usage.model
    fill_in "Prompt", with: @ai_usage.prompt
    fill_in "Provider", with: @ai_usage.provider
    fill_in "Responsed content", with: @ai_usage.responsed_content
    click_on "Create Ai usage"

    assert_text "Ai usage was successfully created"
    click_on "Back"
  end

  test "should update Ai usage" do
    visit ai_usage_url(@ai_usage)
    click_on "Edit this ai usage", match: :first

    fill_in "Category", with: @ai_usage.category
    fill_in "Cost", with: @ai_usage.cost
    fill_in "Model", with: @ai_usage.model
    fill_in "Prompt", with: @ai_usage.prompt
    fill_in "Provider", with: @ai_usage.provider
    fill_in "Responsed content", with: @ai_usage.responsed_content
    click_on "Update Ai usage"

    assert_text "Ai usage was successfully updated"
    click_on "Back"
  end

  test "should destroy Ai usage" do
    visit ai_usage_url(@ai_usage)
    click_on "Destroy this ai usage", match: :first

    assert_text "Ai usage was successfully destroyed"
  end
end
