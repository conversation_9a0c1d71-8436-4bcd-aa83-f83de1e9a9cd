require "application_system_test_case"

class LifeScenariosTest < ApplicationSystemTestCase
  setup do
    @life_scenario = life_scenarios(:one)
  end

  test "visiting the index" do
    visit life_scenarios_url
    assert_selector "h1", text: "Life scenarios"
  end

  test "should create life scenario" do
    visit life_scenarios_url
    click_on "New life scenario"

    fill_in "Postface", with: @life_scenario.postface
    fill_in "Preface", with: @life_scenario.preface
    fill_in "Remark 1", with: @life_scenario.remark_1
    fill_in "Remark 2", with: @life_scenario.remark_2
    fill_in "Remark 3", with: @life_scenario.remark_3
    fill_in "Remark 4", with: @life_scenario.remark_4
    fill_in "Remark 5", with: @life_scenario.remark_5
    fill_in "Remark 6", with: @life_scenario.remark_6
    fill_in "Remark 7", with: @life_scenario.remark_7
    fill_in "Remark 8", with: @life_scenario.remark_8
    fill_in "Score 1", with: @life_scenario.score_1
    fill_in "Score 2", with: @life_scenario.score_2
    fill_in "Score 3", with: @life_scenario.score_3
    fill_in "Score 4", with: @life_scenario.score_4
    fill_in "Score 5", with: @life_scenario.score_5
    fill_in "Score 6", with: @life_scenario.score_6
    fill_in "Score 7", with: @life_scenario.score_7
    fill_in "Score 8", with: @life_scenario.score_8
    fill_in "Title 1", with: @life_scenario.title_1
    fill_in "Title 2", with: @life_scenario.title_2
    fill_in "Title 3", with: @life_scenario.title_3
    fill_in "Title 4", with: @life_scenario.title_4
    fill_in "Title 5", with: @life_scenario.title_5
    fill_in "Title 6", with: @life_scenario.title_6
    fill_in "Title 7", with: @life_scenario.title_7
    fill_in "Title 8", with: @life_scenario.title_8
    fill_in "Written at", with: @life_scenario.written_at
    click_on "Create Life scenario"

    assert_text "Life scenario was successfully created"
    click_on "Back"
  end

  test "should update Life scenario" do
    visit life_scenario_url(@life_scenario)
    click_on "Edit this life scenario", match: :first

    fill_in "Postface", with: @life_scenario.postface
    fill_in "Preface", with: @life_scenario.preface
    fill_in "Remark 1", with: @life_scenario.remark_1
    fill_in "Remark 2", with: @life_scenario.remark_2
    fill_in "Remark 3", with: @life_scenario.remark_3
    fill_in "Remark 4", with: @life_scenario.remark_4
    fill_in "Remark 5", with: @life_scenario.remark_5
    fill_in "Remark 6", with: @life_scenario.remark_6
    fill_in "Remark 7", with: @life_scenario.remark_7
    fill_in "Remark 8", with: @life_scenario.remark_8
    fill_in "Score 1", with: @life_scenario.score_1
    fill_in "Score 2", with: @life_scenario.score_2
    fill_in "Score 3", with: @life_scenario.score_3
    fill_in "Score 4", with: @life_scenario.score_4
    fill_in "Score 5", with: @life_scenario.score_5
    fill_in "Score 6", with: @life_scenario.score_6
    fill_in "Score 7", with: @life_scenario.score_7
    fill_in "Score 8", with: @life_scenario.score_8
    fill_in "Title 1", with: @life_scenario.title_1
    fill_in "Title 2", with: @life_scenario.title_2
    fill_in "Title 3", with: @life_scenario.title_3
    fill_in "Title 4", with: @life_scenario.title_4
    fill_in "Title 5", with: @life_scenario.title_5
    fill_in "Title 6", with: @life_scenario.title_6
    fill_in "Title 7", with: @life_scenario.title_7
    fill_in "Title 8", with: @life_scenario.title_8
    fill_in "Written at", with: @life_scenario.written_at
    click_on "Update Life scenario"

    assert_text "Life scenario was successfully updated"
    click_on "Back"
  end

  test "should destroy Life scenario" do
    visit life_scenario_url(@life_scenario)
    click_on "Destroy this life scenario", match: :first

    assert_text "Life scenario was successfully destroyed"
  end
end
