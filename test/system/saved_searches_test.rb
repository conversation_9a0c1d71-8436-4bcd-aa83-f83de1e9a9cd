require "application_system_test_case"

class SavedSearchesTest < ApplicationSystemTestCase
  setup do
    @saved_search = saved_searches(:one)
  end

  test "visiting the index" do
    visit saved_searches_url
    assert_selector "h1", text: "Saved searches"
  end

  test "should create saved search" do
    visit saved_searches_url
    click_on "New saved search"

    fill_in "Name", with: @saved_search.name
    fill_in "Query", with: @saved_search.query
    fill_in "Search for", with: @saved_search.search_for
    fill_in "Tags", with: @saved_search.tags
    click_on "Create Saved search"

    assert_text "Saved search was successfully created"
    click_on "Back"
  end

  test "should update Saved search" do
    visit saved_search_url(@saved_search)
    click_on "Edit this saved search", match: :first

    fill_in "Name", with: @saved_search.name
    fill_in "Query", with: @saved_search.query
    fill_in "Search for", with: @saved_search.search_for
    fill_in "Tags", with: @saved_search.tags
    click_on "Update Saved search"

    assert_text "Saved search was successfully updated"
    click_on "Back"
  end

  test "should destroy Saved search" do
    visit saved_search_url(@saved_search)
    click_on "Destroy this saved search", match: :first

    assert_text "Saved search was successfully destroyed"
  end
end
