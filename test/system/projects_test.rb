require "application_system_test_case"

class ProjectsTest < ApplicationSystemTestCase
  setup do
    @project = projects(:one)
  end

  test "visiting the index" do
    visit projects_url
    assert_selector "h1", text: "Projects"
  end

  test "should create project" do
    visit projects_url
    click_on "New project"

    fill_in "Actual end at", with: @project.actual_end_at
    fill_in "Begin at", with: @project.begin_at
    fill_in "Budgets", with: @project.budgets
    check "Is pinned" if @project.is_pinned
    fill_in "Issues", with: @project.issues
    fill_in "Name", with: @project.name
    fill_in "Objective", with: @project.objective
    fill_in "Risks", with: @project.risks
    fill_in "Schedules", with: @project.schedules
    fill_in "Stakeholders", with: @project.stakeholders
    fill_in "Target end at", with: @project.target_end_at
    click_on "Create Project"

    assert_text "Project was successfully created"
    click_on "Back"
  end

  test "should update Project" do
    visit project_url(@project)
    click_on "Edit this project", match: :first

    fill_in "Actual end at", with: @project.actual_end_at
    fill_in "Begin at", with: @project.begin_at
    fill_in "Budgets", with: @project.budgets
    check "Is pinned" if @project.is_pinned
    fill_in "Issues", with: @project.issues
    fill_in "Name", with: @project.name
    fill_in "Objective", with: @project.objective
    fill_in "Risks", with: @project.risks
    fill_in "Schedules", with: @project.schedules
    fill_in "Stakeholders", with: @project.stakeholders
    fill_in "Target end at", with: @project.target_end_at
    click_on "Update Project"

    assert_text "Project was successfully updated"
    click_on "Back"
  end

  test "should destroy Project" do
    visit project_url(@project)
    click_on "Destroy this project", match: :first

    assert_text "Project was successfully destroyed"
  end
end
