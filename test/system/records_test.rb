require "application_system_test_case"

class RecordsTest < ApplicationSystemTestCase
  setup do
    @record = records(:one)
  end

  test "visiting the index" do
    visit records_url
    assert_selector "h1", text: "Records"
  end

  test "should create record" do
    visit records_url
    click_on "New record"

    fill_in "Frontmatters", with: @record.frontmatters
    fill_in "Generated content", with: @record.generated_content
    fill_in "Highlighted text", with: @record.highlighted_text
    fill_in "Kind", with: @record.kind
    fill_in "Prompt", with: @record.prompt
    fill_in "Source content", with: @record.source_content
    fill_in "Summary", with: @record.summary
    fill_in "Text content", with: @record.text_content
    fill_in "Title", with: @record.title
    fill_in "Url", with: @record.url
    click_on "Create Record"

    assert_text "Record was successfully created"
    click_on "Back"
  end

  test "should update Record" do
    visit record_url(@record)
    click_on "Edit this record", match: :first

    fill_in "Frontmatters", with: @record.frontmatters
    fill_in "Generated content", with: @record.generated_content
    fill_in "Highlighted text", with: @record.highlighted_text
    fill_in "Kind", with: @record.kind
    fill_in "Prompt", with: @record.prompt
    fill_in "Source content", with: @record.source_content
    fill_in "Summary", with: @record.summary
    fill_in "Text content", with: @record.text_content
    fill_in "Title", with: @record.title
    fill_in "Url", with: @record.url
    click_on "Update Record"

    assert_text "Record was successfully updated"
    click_on "Back"
  end

  test "should destroy Record" do
    visit record_url(@record)
    click_on "Destroy this record", match: :first

    assert_text "Record was successfully destroyed"
  end
end
