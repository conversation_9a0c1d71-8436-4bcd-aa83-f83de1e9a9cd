module ApplicationHelper

    def markdowned(text)
        return if text.nil?
        markdown = Redcarpet::Markdown.new(Redcarpet::Render::HTML.new(render_options = {hard_wrap: true}), autolink: true,
                                                        tables: true,
                                                        fenced_code_blocks: true,
                                                        strikethrough: true,
                                                        highlight: true,
                                                        footnotes: true,
                                                        space_after_headers: true,
                                                        hard_wrap: true,
                                                        no_intra_emphasis: true,
                                                        lax_spacing: true
        )
        markdown.render(text)
    end

    def md_todo_replaced(text)
        return if text.nil?
        text.gsub("- [ ] ", "- ◻️ ").gsub("- [x] ", "- ✅ ")
    end

    def first_line(text)
        text.split("\n").first
    end

    def last_line(text)
        text.split("\n").last
    end
end
