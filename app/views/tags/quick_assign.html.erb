<%= render @record %>

<%= form_with(model: @record, local: true) do |form| %>
  <%= form.label :tagss %>
  <%= form.text_field :tagss, autofocus: true %>
  <%= form.submit "Quick Assign Tags" %>
<% end %>

<div>
  <%= render 'records/attachment_list', record: @record %>
</div>

<% unless @record.highlighted_text.blank? %>
  <div style="border-left: 5px solid gold;padding:1em;">
    <%== @record.markdowned_highlighted_text %>
  </div>
<% end %>

<% unless @record.summary.blank? %>
  <div>
    <%== markdowned @record.summary %>
  </div>
<% end %>

<div>
  <%== @record.markdowned_content %>
</div>

<% unless @record.generated_content.blank? %>
  <div>
    <details>
      <summary>Prompt: <%= @record.prompt[0..20] %></summary>
      <p><%= @record.system_prompt %></p>
      <p><%= @record.prompt %></p>
    </details>
    <h2>Generated Content</h2>
    <%== @record.markdowned_generated_content %>
  </div>
<% end %>

<% unless @record.source_content.blank? %>
  <details>
    <summary>Source Content</summary>
    <div>
      <%== markdowned @record.source_content %>
    </div>
  </details>
<% end %>

<p>Existing tags</p>
<% @tags.each do |tag| %>
  <div class="tag"><%= tag.name %></div>
<% end %>

<p><%= link_to "Random", quick_assign_tags_path(random: "true") %></p>