<%= form_with(model: tag) do |form| %>
  <% if tag.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(tag.errors.count, "error") %> prohibited this tag from being saved:</h2>

      <ul>
        <% tag.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name %>
  </div>

  <div>
    <%= form.label :alias_of_tag_id, style: "display: block" %>
    <%= form.text_field :alias_of_tag_id %>
  </div>

  <div>
    <%= form.label :color, style: "display: block" %>
    <%= form.text_field :color %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
