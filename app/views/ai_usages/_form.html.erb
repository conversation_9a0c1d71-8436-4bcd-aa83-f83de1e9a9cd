<%= form_with(model: ai_usage) do |form| %>
  <% if ai_usage.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(ai_usage.errors.count, "error") %> prohibited this ai_usage from being saved:</h2>

      <ul>
        <% ai_usage.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :prompt, style: "display: block" %>
    <%= form.text_area :prompt %>
  </div>

  <div>
    <%= form.label :responsed_content, style: "display: block" %>
    <%= form.text_area :responsed_content %>
  </div>

  <div>
    <%= form.label :cost, style: "display: block" %>
    <%= form.text_field :cost %>
  </div>

  <div>
    <%= form.label :provider, style: "display: block" %>
    <%= form.text_field :provider %>
  </div>

  <div>
    <%= form.label :model, style: "display: block" %>
    <%= form.text_field :model %>
  </div>

  <div>
    <%= form.label :category, style: "display: block" %>
    <%= form.text_field :category %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
