<div id="<%= dom_id daily_statistic %>">
  <p>
    <strong>Date at:</strong>
    <%= daily_statistic.date_at %>
  </p>

  <p>
    <strong>Text content total length:</strong>
    <%= daily_statistic.text_content_total_length %>
  </p>

  <p>
    <strong>Post it new count:</strong>
    <%= daily_statistic.post_it_new_count %>
  </p>

  <p>
    <strong>Post it total:</strong>
    <%= daily_statistic.post_it_total %>
  </p>

  <p>
    <strong>Todo new count:</strong>
    <%= daily_statistic.todo_new_count %>
  </p>

  <p>
    <strong>Todo update count:</strong>
    <%= daily_statistic.todo_update_count %>
  </p>

  <p>
    <strong>Todo total:</strong>
    <%= daily_statistic.todo_total %>
  </p>

  <p>
    <strong>Follow up count:</strong>
    <%= daily_statistic.follow_up_count %>
  </p>

  <p>
    <strong>People count:</strong>
    <%= daily_statistic.people_count %>
  </p>

  <p>
    <strong>Book count:</strong>
    <%= daily_statistic.book_count %>
  </p>

  <p>
    <strong>Checking count:</strong>
    <%= daily_statistic.checking_count %>
  </p>

  <p>
    <strong>Atomy new count:</strong>
    <%= daily_statistic.atomy_new_count %>
  </p>

  <p>
    <strong>Atomy total count:</strong>
    <%= daily_statistic.atomy_total_count %>
  </p>

  <p>
    <strong>Records new count:</strong>
    <%= daily_statistic.records_new_count %>
  </p>

  <p>
    <strong>Records total count:</strong>
    <%= daily_statistic.records_total_count %>
  </p>

</div>
