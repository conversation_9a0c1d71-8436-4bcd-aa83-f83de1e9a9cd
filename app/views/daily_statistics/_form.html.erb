<%= form_with(model: daily_statistic) do |form| %>
  <% if daily_statistic.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(daily_statistic.errors.count, "error") %> prohibited this daily_statistic from being saved:</h2>

      <ul>
        <% daily_statistic.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :date_at, style: "display: block" %>
    <%= form.date_field :date_at %>
  </div>

  <div>
    <%= form.label :text_content_total_length, style: "display: block" %>
    <%= form.number_field :text_content_total_length %>
  </div>

  <div>
    <%= form.label :post_it_new_count, style: "display: block" %>
    <%= form.number_field :post_it_new_count %>
  </div>

  <div>
    <%= form.label :post_it_total, style: "display: block" %>
    <%= form.number_field :post_it_total %>
  </div>

  <div>
    <%= form.label :todo_new_count, style: "display: block" %>
    <%= form.number_field :todo_new_count %>
  </div>

  <div>
    <%= form.label :todo_update_count, style: "display: block" %>
    <%= form.number_field :todo_update_count %>
  </div>

  <div>
    <%= form.label :todo_total, style: "display: block" %>
    <%= form.number_field :todo_total %>
  </div>

  <div>
    <%= form.label :follow_up_count, style: "display: block" %>
    <%= form.number_field :follow_up_count %>
  </div>

  <div>
    <%= form.label :people_count, style: "display: block" %>
    <%= form.number_field :people_count %>
  </div>

  <div>
    <%= form.label :book_count, style: "display: block" %>
    <%= form.number_field :book_count %>
  </div>

  <div>
    <%= form.label :checking_count, style: "display: block" %>
    <%= form.number_field :checking_count %>
  </div>

  <div>
    <%= form.label :atomy_new_count, style: "display: block" %>
    <%= form.number_field :atomy_new_count %>
  </div>

  <div>
    <%= form.label :atomy_total_count, style: "display: block" %>
    <%= form.number_field :atomy_total_count %>
  </div>

  <div>
    <%= form.label :records_new_count, style: "display: block" %>
    <%= form.number_field :records_new_count %>
  </div>

  <div>
    <%= form.label :records_total_count, style: "display: block" %>
    <%= form.number_field :records_total_count %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
