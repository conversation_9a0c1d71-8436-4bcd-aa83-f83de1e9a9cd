

<h1>Daily statistics</h1>

<form action="/daily_statistics/calculate_for_day" method="get">
  <input type="date" name="date">
  <input type="submit" value="Calculate">
</form>

<div id="daily_statistics">
  <table>
    <thead>
      <tr>
        <th>Date</th>
        <th>Records total count</th>
        <th>Records new count</th>
        <th>Text content total length</th>
        <th>Highlighted text total length</th>
        <th>Post-it new count</th>
        <th>Post-it total</th>
        <th>Todo new count</th>
        <th>Todo update count</th>
        <th>Todo total</th>
        <th>Follow-up count</th>
        <th>People count</th>
        <th>Book count</th>
        <th>Checking count</th>
        <th>Atomy new count</th>
        <th>Atomy total count</th>
      </tr>
    </thead>
    <tbody>
  <% @daily_statistics.each do |daily_statistic| %>
    <tr>
      <td><%= daily_statistic.date_at %></td>
      <td><%= daily_statistic.records_total_count %></td>
      <td><%= daily_statistic.records_new_count %></td>
      <td><%= daily_statistic.text_content_total_length %></td>
      <td><%= daily_statistic.highlighted_text_total_length %></td>
      <td><%= daily_statistic.post_it_new_count %></td>
      <td><%= daily_statistic.post_it_total %></td>
      <td><%= daily_statistic.todo_new_count %></td>
      <td><%= daily_statistic.todo_update_count %></td>
      <td><%= daily_statistic.todo_total %></td>
      <td><%= daily_statistic.follow_up_count %></td>
      <td><%= daily_statistic.people_count %></td>
      <td><%= daily_statistic.book_count %></td>
      <td><%= daily_statistic.checking_count %></td>
      <td><%= daily_statistic.atomy_new_count %></td>
      <td><%= daily_statistic.atomy_total_count %></td>
    </tr>
  <% end %>
    </tbody>
  </table>
</div>