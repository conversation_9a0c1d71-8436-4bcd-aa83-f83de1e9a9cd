<div id="project-show" class="container wide">

  <h1><%= @project.name %></h1>

  <% if @project.is_archived %>
    <p>
      <strong>Archived</strong>
    </p>
  <% end %>

  <div>
    <%= link_to [@project, :progresses] do %>
      <%= @project.last_progress %>
      <progress value="<%= @project.last_progress %>" min="0" max="100"></progress>
    <% end %>
  </div>

  <div>
    <%== markdowned md_todo_replaced @project.last_objective %>
  </div>

  <div>
    <%= link_to "Edit", edit_project_path(@project), class: "button" %>
  </div>

  <div>
    <dl>
      <% if @project.begin_at.present? %>
        <dt>Begin at</dt>
        <dd><%= @project.begin_at %> </dd>
      <% end %>
      <% if @project.target_end_at.present? %>
        <dt>Target end at</dt>
        <dd><%= @project.target_end_at %> </dd>
      <% end %>
      <% if @project.actual_end_at.present? %>
        <dt>Actual end at</dt>
        <dd><%= @project.actual_end_at %> </dd>
      <% end %>
      <% if @project.next_milestone_at.present? %>
        <dt>Next milestone at</dt>
        <dd><%= @project.next_milestone_at %> </dd>
      <% end %>
    </dl>
  </div>


  <% if @project.last_stakeholders.present? %>
    <div>
      <h2>Stakeholders</h2>
      <%== markdowned md_todo_replaced @project.last_stakeholders %>
    </div>
  <% end %>

  <% if @project.last_schedules.present? %>
    <div>
      <h2>Schedules</h2>
      <%== markdowned md_todo_replaced @project.last_schedules %>
    </div>
  <% end %>

  <% if @project.last_budgets.present? %>
    <div>
      <h2>Budgets</h2>
      <%== markdowned md_todo_replaced @project.last_budgets %>
    </div>
  <% end %>

  <% if @project.last_risks.present? %>
    <div>
      <h2>Risks</h2>
      <%== markdowned md_todo_replaced @project.last_risks %>
    </div>
  <% end %>

  <% if @project.last_issues.present? %>
    <div>
      <h2>Issues</h2>
      <%== markdowned md_todo_replaced @project.last_issues %>
    </div>
  <% end %>


  <h2>Todos</h2>

  <p><%= link_to "New Todo", new_record_path(project_id: @project.id, kind: "todo") %></p>

  <ul>
    <% @project.todos.each do |record| %>
      <% unless record.is_done? %>
        <li><%= render "records/todos_item", record: record %></li>
      <% end %>
    <% end %>
  </ul>

  <small>
    <details>
      <summary>
        Done Todos
        <small>(<%= @project.done_todos.count %>)</small>
      </summary>
      <ul>
        <% @project.done_todos.each do |record| %>
          <li><%= render "records/todos_item", record: record %></li>
        <% end %>
      </ul>
    </details>
  </small>

  <h2>Records</h2>

  <p><%= link_to "New Record", new_record_path(project_id: @project.id) %></p>

  <%= render "records/grid", records: @project.records %>


</div>