<%= form_with(model: project) do |form| %>
  <% if project.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(project.errors.count, "error") %> prohibited this project from being saved:</h2>

      <ul>
        <% project.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name %>
  </div>

  <div>
    <%= form.label :is_archived, style: "display: block" %>
    <%= form.check_box :is_archived %>
  </div>

  <div>
    <%= form.label :objective, "Initial Objective", style: "display: block" %>
    <%= form.text_area :objective %>
  </div>

  <div>
    <%= form.label :begin_at, style: "display: block" %>
    <%= form.date_field :begin_at %>
  </div>

  <div>
    <%= form.label :target_end_at, style: "display: block" %>
    <%= form.date_field :target_end_at %>
  </div>

  <div>
    <%= form.label :actual_end_at, style: "display: block" %>
    <%= form.date_field :actual_end_at %>
  </div>

  <div>
    <%= form.label :next_milestone_at, style: "display: block" %>
    <%= form.date_field :next_milestone_at %>
  </div>

  <div>
    <%= form.label :stakeholders, "Initial Stakeholders", style: "display: block" %>
    <%= form.text_area :stakeholders %>
  </div>

  <div>
    <%= form.label :schedules, "Initial Schedules", style: "display: block" %>
    <%= form.text_area :schedules %>
  </div>

  <div>
    <%= form.label :budgets, "Initial Budgets", style: "display: block" %>
    <%= form.text_area :budgets %>
  </div>

  <div>
    <%= form.label :risks, "Initial Risks", style: "display: block" %>
    <%= form.text_area :risks %>
  </div>

  <div>
    <%= form.label :issues, "Initial Issues", style: "display: block" %>
    <%= form.text_area :issues %>
  </div>

  <div>
    <%= form.label :is_pinned, style: "display: block" %>
    <%= form.check_box :is_pinned %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
