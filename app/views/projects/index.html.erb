<div class="container wider">


  <h1>Projects</h1>

  <p><%= link_to "New project", new_project_path, class: "button" %></p>

  <div id="projects">

    <ul>
      <% [[@pinned_projects, "Pinned"], [@projects, "All"]].each do |projects, category| %>
        <li>
          <%= category %>
          <ul>
            <% projects.each do |project| %>
              <li>
                <%= link_to project, project %>
                <small>
                  <%= project.next_milestone_at %>
                </small>
              </li>
            <% end %>
          </ul>
        </li>
      <% end %>
    </ul>


    <% if @pinned_projects.any? %>
      <%= render "table", projects: @pinned_projects %>
    <% end %>

    <% if @projects.any? %>
      <%= render "table", projects: @projects %>
    <% end %>

    <% if @archived_projects.any? %>
      <h2>Archived</h2>
      <ul>
        <% @archived_projects.each do |project| %>
          <li>
            <%= link_to project, project %>
          </li>
        <% end %>
      </ul>
    <% end %>

  </div>




</div>