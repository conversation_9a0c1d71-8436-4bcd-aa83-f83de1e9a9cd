<%= form_with(model: daily_checking) do |form| %>
  <% if daily_checking.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(daily_checking.errors.count, "error") %> prohibited this daily_checking from being saved:</h2>

      <ul>
        <% daily_checking.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :daily_checking_template_id, style: "display: block" %>
    <%= form.text_field :daily_checking_template_id %>
  </div>

  <div>
    <%= form.label :date_at, style: "display: block" %>
    <%= form.date_field :date_at %>
  </div>

  <div>
    <%= form.label :is_checked, style: "display: block" %>
    <%= form.check_box :is_checked %>
  </div>

  <div>
    <%= form.label :remark, style: "display: block" %>
    <%= form.text_area :remark %>
  </div>

  <div>
    <%= form.label :rank, style: "display: block" %>
    <%= form.number_field :rank %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
