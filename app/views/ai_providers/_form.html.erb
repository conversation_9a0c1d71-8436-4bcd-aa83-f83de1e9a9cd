<%= form_with(model: ai_provider) do |form| %>
  <% if ai_provider.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(ai_provider.errors.count, "error") %> prohibited this ai_provider from being saved:</h2>

      <ul>
        <% ai_provider.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name %>
  </div>

  <div>
    <%= form.label :slug, style: "display: block" %>
    <%= form.text_field :slug %>
  </div>

  <div>
    <%= form.label :api_base_url, style: "display: block" %>
    <%= form.text_field :api_base_url %>
  </div>

  <div>
    <%= form.label :api_key, style: "display: block" %>
    <%= form.text_field :api_key %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
