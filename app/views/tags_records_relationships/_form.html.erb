<%= form_with(model: tags_records_relationship) do |form| %>
  <% if tags_records_relationship.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(tags_records_relationship.errors.count, "error") %> prohibited this tags_records_relationship from being saved:</h2>

      <ul>
        <% tags_records_relationship.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :tag_id, style: "display: block" %>
    <%= form.text_field :tag_id %>
  </div>

  <div>
    <%= form.label :record_id, style: "display: block" %>
    <%= form.text_field :record_id %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
