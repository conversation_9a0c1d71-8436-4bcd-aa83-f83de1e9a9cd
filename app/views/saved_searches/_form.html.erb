<%= form_with(model: saved_search) do |form| %>
  <% if saved_search.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(saved_search.errors.count, "error") %> prohibited this saved_search from being saved:</h2>

      <ul>
        <% saved_search.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name %>
  </div>

  <div>
    <%= form.label :query, style: "display: block" %>
    <%= form.text_field :query, autofocus: true %>
  </div>

  <div>
    <%= form.label :tags, style: "display: block" %>
    <%= form.text_field :tags %>
    <p><small>comma separated</small></p>
  </div>

  <div>
    <%= form.label :search_for, style: "display: block" %>
    <%= form.text_field :search_for %>
    <p><small>empty, url, frontmatter, title</small></p>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
