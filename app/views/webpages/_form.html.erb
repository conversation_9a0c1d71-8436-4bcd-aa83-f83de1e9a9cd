<%= form_with(model: webpage) do |form| %>
  <% if webpage.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(webpage.errors.count, "error") %> prohibited this webpage from being saved:</h2>

      <ul>
        <% webpage.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :record_id, style: "display: block" %>
    <%= form.text_field :record_id %>
  </div>

  <div>
    <%= form.label :title, style: "display: block" %>
    <%= form.text_field :title %>
  </div>

  <div>
    <%= form.label :html, style: "display: block" %>
    <%= form.text_area :html %>
  </div>

  <div>
    <%= form.label :css, style: "display: block" %>
    <%= form.text_area :css %>
  </div>

  <div>
    <%= form.label :js, style: "display: block" %>
    <%= form.text_area :js %>
  </div>

  <div>
    <%= form.label :filename, style: "display: block" %>
    <%= form.text_field :filename %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
