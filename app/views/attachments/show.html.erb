<div class="container">
    <% a = @blob.attachments.first %>

    <h1><%= a.filename %></h1>

    <p>
        <strong>Record:</strong>
        <%= link_to a.record.id, a.record %>
        <%= link_to a.record.title, a.record %>
    </p>


    <% if a.image? %>
        <%= image_tag a.variant(resize_to_limit: [800,800]) if a.variable? %>
    <% elsif a.previewable? %>
        <%= image_tag a.preview(resize_to_limit: [800,800]) %>
    <% end %>

    <%== markdowned @blob.custom_metadata && @blob.custom_metadata["ocr_content"] %>

    <details>
        <summary>Metadata</summary>
        <div>
            <%= @blob.custom_metadata %>
        </div>
        <div>
            <%= @blob.metadata %>
        </div>
    </details>

    <p>
        <%= link_to "OCR", attachment_ocr_path(@blob.key), class: "button" %>
    </p>

    <p>&nbsp;</p>
    <details>
        <summary>Delete this file</summary>
        <%= button_to "Delete this file", attachment_delete_path(@blob.key), method: :post %>
    </details>
</div>
