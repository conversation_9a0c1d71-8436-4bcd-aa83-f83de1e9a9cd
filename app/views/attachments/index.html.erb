<h1>Attachments</h1>

<p>
    Total: <%= number_to_human_size @total_storage_size %>.
    <%= @available_space %>
    <br>
    Order by <a href="<%= attachments_path %>">date</a> or <a href="<%= attachments_path(order: 'size') %>">size</a>.</p>

<div class="grid">
    <% @attachments.each do |a| %>
        <% cache [a] do %>
            <figure>
                <%= link_to attachment_path(a.key) do %>
                    <%= image_tag a.preview(resize_to_limit: [200, 200]) if a.previewable? %>
                    <%= image_tag a.variant(resize_to_limit: [200, 200]) if a.variable? %>
                <% end %>
                <figcaption>
                    <%= link_to a.record.title, a.record %>
                    <small><%= link_to "(#{number_to_human_size a.blob.byte_size})", attachment_path(a.key) %></small>
                </figcaption>
            </figure>
        <% end %>
    <% end %>
</div>

<a name="more"></a>
<p><%= link_to "More...", attachments_path(order: params[:order], limit: @limit*2, anchor: "more") %></p>