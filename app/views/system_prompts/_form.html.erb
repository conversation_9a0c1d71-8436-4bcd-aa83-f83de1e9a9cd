<%= form_with(model: system_prompt) do |form| %>
  <% if system_prompt.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(system_prompt.errors.count, "error") %> prohibited this system_prompt from being saved:</h2>

      <ul>
        <% system_prompt.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :system_prompt, style: "display: block" %>
    <%= form.text_area :system_prompt %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
