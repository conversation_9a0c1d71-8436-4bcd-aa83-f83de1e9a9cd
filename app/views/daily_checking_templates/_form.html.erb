<%= form_with(model: daily_checking_template) do |form| %>
  <% if daily_checking_template.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(daily_checking_template.errors.count, "error") %> prohibited this daily_checking_template from being saved:</h2>

      <ul>
        <% daily_checking_template.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :title, style: "display: block" %>
    <%= form.text_field :title %>
  </div>

  <div>
    <%= form.label :is_active, style: "display: block" %>
    <%= form.check_box :is_active %>
  </div>

  <div>
    <%= form.label :remarks, style: "display: block" %>
    <%= form.text_area :remarks %>
  </div>

  <div>
    <%= form.label :rank, style: "display: block" %>
    <%= form.number_field :rank %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
