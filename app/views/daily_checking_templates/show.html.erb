<div class="container">


  <p>
    <%= link_to "All templates", daily_checking_templates_path %>
  </p>

  <h1><%= @daily_checking_template.title %></h1>

  <p class="markdowned-margin-fix"><%== markdowned @daily_checking_template.remarks %></p>

  <h2>Previous checkings</h2>

  <% @daily_checking_template.daily_checkings.order(date_at: :desc).each do |daily_checking| %>
      <p>
        <% if daily_checking.is_checked %>
          ✅
        <% else %>
          ⬜️
        <% end %>
        <%= link_to daily_checking.date_at.strftime("%Y-%m-%d"), daily_checking %>
        <%== markdowned daily_checking.remark %>
      </p>
  <% end %>

  <div>
    <!-- <%= link_to "Edit this daily checking template", edit_daily_checking_template_path(@daily_checking_template) %> | -->
    <!-- <%= link_to "Back to daily checking templates", daily_checking_templates_path %> -->

    <!-- <%= button_to "Destroy this daily checking template", @daily_checking_template, method: :delete %> -->
  </div>


</div>