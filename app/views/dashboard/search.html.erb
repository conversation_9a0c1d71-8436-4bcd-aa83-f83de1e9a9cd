<div class="container wide">
  <h1>Search</h1>

  <form action="/records" method="get">
    <label>Query
      <input type="text" name="query" autofocus>
    </label>
    <label>Tags
      <input type="text" name="tags">
    </label>
    <input type="hidden" name="search_for" value="title">
    <button type="submit">Search</button>
  </form>

  <h2>Saved Searches</h2>
  <%= link_to "+ Saved Search", new_saved_search_path %>
  <%= link_to "Edit", saved_searches_path %>
  <div class="saved_searches-tags">
    <% SavedSearch.all.each do |saved_search| %>
        <div class="tag">
          <%= link_to saved_search, records_path(query: saved_search.query, tags: saved_search.tags, search_for: saved_search.search_for) %>
        </div>
    <% end %>
  </div>

  <h2>Tags</h2>
  <div id="tags">
    <% @tags.each do |tag| %>
      <div class="tag tag-count-<%= tag.records.count / 5.to_i %>">
        <%= link_to tag.name, tag %>
        <small><%= tag.records.count %></small>
      </div>
    <% end %>
  </div>

  <p><%= link_to "Quick Assign Tags", quick_assign_tags_path %></p>
</div>
