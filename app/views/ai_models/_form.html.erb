<%= form_with(model: ai_model) do |form| %>
  <% if ai_model.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(ai_model.errors.count, "error") %> prohibited this ai_model from being saved:</h2>

      <ul>
        <% ai_model.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :ai_provider_id, style: "display: block" %>
    <%= form.text_field :ai_provider_id %>
  </div>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name %>
  </div>

  <div>
    <%= form.label :slug, style: "display: block" %>
    <%= form.text_field :slug %>
  </div>

  <div>
    <%= form.label :api_slug, style: "display: block" %>
    <%= form.text_field :api_slug %>
  </div>

  <div>
    <%= form.label :usd_per_million_input, style: "display: block" %>
    <%= form.text_field :usd_per_million_input %>
  </div>

  <div>
    <%= form.label :usd_per_million_output, style: "display: block" %>
    <%= form.text_field :usd_per_million_output %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
