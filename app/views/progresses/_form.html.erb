<%= form_with(model: [progress.project, progress]) do |form| %>
  <% if progress.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(progress.errors.count, "error") %> prohibited this progress from being saved:</h2>

      <ul>
        <% progress.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.hidden_field :project_id %>
  </div>

  <div>
    <%= form.label :progress, style: "display: block" %>
    <%= form.number_field :progress, autofocus: true %>
  </div>

  <div>
    <%= form.label :remark, style: "display: block" %>
    <%= form.text_area :remark %>
  </div>

  <div>
    <%= form.label :objective, style: "display: block" %>
    <%= form.text_area :objective %>
  </div>

  <div>
    <%= form.label :stakeholders, style: "display: block" %>
    <%= form.text_area :stakeholders %>
  </div>

  <div>
    <%= form.label :schedules, style: "display: block" %>
    <%= form.text_area :schedules %>
  </div>

  <div>
    <%= form.label :budgets, style: "display: block" %>
    <%= form.text_area :budgets %>
  </div>

  <div>
    <%= form.label :risks, style: "display: block" %>
    <%= form.text_area :risks %>
  </div>

  <div>
    <%= form.label :issues, style: "display: block" %>
    <%= form.text_area :issues %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
