<div class="container">
    <nav>
        <%= link_to "To-do", todos_records_path %>
        <%= link_to "Done", done_todos_records_path %>
        <%= link_to "✨ AI Assist", todos_ai_records_path, class: "button" %>
    </nav>

    <div class="assistant-chat-message">
        <%== markdowned @ai_suggestion %>
    </div>

    <hr>

    <table>
        <tr>
            <th></th>
            <th></th>
            <th>Task</th>
        </tr>

        <% titles = ["Pinned", "Today", "Past", "Future", "Other"] %>
        <% [@pinned_todos, @today_todos, @past_todos, @future_todos, @other_todos].each_with_index do |records, index| %>
            <% if records.any? %>
                <tr>
                    <th colspan="3" style="text-align: left;"><%= titles[index] %></th>
                </tr>
            <% end %>
            <% records.each do |record| %>
                <% unless record.is_done? %>
                    <%= render "todos_row", record: record %>
                <% end %>
            <% end %>
        <% end %>
    </table>

</div>