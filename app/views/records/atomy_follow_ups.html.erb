<div class="container">
    <%= render "kinds_nav" %>

    <h1>Follow-ups</h1>

    <p>
        <%= link_to "👤 People", people_records_path %>

        <%= link_to "💬 Follow-up", atomy_follow_ups_records_path %>
    </p>
    <p>&nbsp;</p>

    <table>
        <tr>
            <th></th>
            <th></th>
            <th></th>
            <th>Task</th>
        </tr>
    <% @records.each do |record| %>
        <% unless record.is_done? %>
            <% cache ["record follow-up", record] do %>
                <tr>
                    <td><%= link_to record.id, record %></td>
                    <td>
                        <%= "✅" if record.is_done? %>
                        <%= "⬜️" unless record.is_done? %>
                    </td>
                    <td>
                        <% if record.parent %>
                            <%= link_to record.parent.title, record.parent %>
                        <% end %>
                    </td>
                    <td>
                        <strong><%= record.front_matter && record.front_matter["due_at"] || "" %></strong>
                        <%= record.title %>
                    </td>
                </tr>
            <% end %>
        <% end %>
    <% end %>
    </table>

    <p><%= link_to "New Atomy Follow-up", new_record_path(kind: "atomy follow-up") %></p>
</div>
