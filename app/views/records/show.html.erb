

<div class="container record-show">

  <% cache ["record show", @record] do %>
    <% if @record.parent %>
      <p>
        <strong>↰</strong>
        <% if @record.parent %>
          <%= link_to @record.parent.title, @record.parent %>
        <% end %>
      </p>
    <% end %>

    <h1>
      <%= render 'records/title_with_emojis', record: @record %>
    </h1>

    <input type="checkbox" id="show-details">
    <label for="show-details">Show details</label>

    <p class="hide-until-details-checked">
      <%= link_to @record.id, @record %>
      <%= link_to "Edit", edit_record_path(@record), class: "secondary button" %>

      <% if @record.ai_process_status.present? %>
        <% if (@record.ai_process_status > 0) and (@record.ai_process_status < 3) %>
          <span><small>AI process status: <%= @record.ai_process_status %></small></span>
        <% end %>
      <% end %>
    </p>

    <div>
      <dl>
        <dt>tags</dt>
        <dd>
          <% @record.tags.each do |tag| %>
            <%= link_to "##{tag.name}", tag %>
          <% end %>

          <details class="quick-edit">
            <summary>Quick Edit</summary>
            <%= form_with(model: @record) do |form| %>
              <%= form.label :title %>
              <%= form.text_field :title %>
              <%= form.label :tags %>
              <%= form.text_field :tagss %>

              <%= form.submit %>
            <% end %>

            <details>
              <summary>Tags</summary>
              <% Tag.all.order(name: :asc).each do |tag| %>
                <%= tag.name %>
              <% end %>
            </details>

            <p>
              <strong>Quick set color</strong>
              <% Record.color_options.each do |color| %>
                <%= link_to record_set_color_path(@record, color: color) do %>
                  <span class="color-dot" style="background-color: <%= color %>"></span>
                <% end %>
              <% end %>
              <%= link_to record_set_color_path(@record, color: "") do %>
                <span class="color-dot" style="background-color: #efefef"></span>
              <% end %>
            </p>

            <p>
              <%= link_to "Edit", edit_record_path(@record), class: "secondary button" %>
              &nbsp;
              <%= link_to "Reset title", record_reset_title_path(@record), class:'button' %>
            </p>


            <% if @record.is_journal? %>
            <p>
              Schedule to:
              <%= link_to "Yesterday", record_set_to_date_path(@record, date: Date.yesterday) %>

              <%= link_to "Today", record_set_today_path(@record), class: "button" %>
            </p>
          <% end %>

          </details>
        </dd>
      </dl>

    </div>

    <div class="hide-until-details-checked">
      <p>
        <%= link_to @record.date_at, calendar_path(start_date: @record.date_at) %>
      </p>

      <% if @record.is_todo? %>
        <p>
          Schedule to:
          <%= link_to "Today", record_set_today_path(@record), class: "button" %>

          <%= link_to "Tomorrow", record_set_to_date_path(@record, date: Date.tomorrow) %>

          <%= link_to "Weekend", record_set_to_date_path(@record, date: Date.today.end_of_week(:sunday)) %>

          <%= link_to "Next Week", record_set_to_date_path(@record, date: Date.today.beginning_of_week(:monday).next_week) %>
        </p>
      <% end %>


    </div>


    <div class="hide-until-details-checked">
      <dl>
      <% if @record.project_id %>
        <dt>Project</dt>
        <dd><%= link_to @record.project.name, @record.project %></dd>
      <% end %>
      <% unless @record.front_matter.blank? %>
        <% @record.front_matter.each do |key, value| %>
          <dt><%= key %></dt>
          <dd><%= value %></dd>
        <% end %>
      <% end %>
      <% if @record.readed %>
        <dt>👀 Read</dt>
        <dd>Yes
          <%= link_to "x", mark_not_readed_record_path(@record) %>
        </dd>
      <% end %>
      <% if @record.is_pinned %>
        <dt>📌 Pinned</dt>
        <dd>Yes
          <%= link_to "x", mark_unpinned_record_path(@record) %>
        </dd>
      <% end %>
      <% if @record.is_nice_to_have %>
        <dt>Nice to have</dt>
        <dd>Yes
          <%= link_to "x", mark_not_nice_to_have_record_path(@record) %>
        </dd>
      <% end %>
      <% if @record.is_archived %>
        <dt>🗑️ Archived</dt>
        <dd>Yes
          <%= link_to "x", mark_not_archived_record_path(@record) %>
        </dd>
      <% end %>
      <dt>Created at</dt>
      <dd><%= @record.created_at.to_date %></dd>
      <% if @record.updated_at.to_date != @record.created_at.to_date %>
        <dt>Updated at</dt>
        <dd><%= @record.updated_at.to_date %></dd>
      <% end %>
      </dl>
    </div>

    <% unless @record.url.blank? %>
      <p>
        <small><%= link_to @record.url, @record.url %></small>
      </p>
    <% end %>

    <% if @record.attachments.count == 1 %>
      <%= render 'records/attachment_one', record: @record %>
    <% end %>

    <% unless @record.highlighted_text.blank? %>
      <div style="border-left: 5px solid gold;padding:1em;">
        <%== @record.markdowned_highlighted_text %>
      </div>
    <% end %>

    <% unless @record.url.blank? %>
      <% if @record.is_youtube? %>
        <div style="text-align:center;">
          <iframe width="100%" height="360" src="https://www.youtube.com/embed/<%= @record.youtube_id %>" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
      <% end %>
    <% end %>

    <% unless @record.generated_content.blank? %>
      <div class="generated-content">
        <details>
          <summary>Prompt: <%= @record.prompt[0..20] %></summary>
          <p><%= @record.system_prompt %></p>
          <p><%= @record.prompt %></p>
        </details>
        <h2>Generated Content</h2>
        <%== @record.markdowned_generated_content %>
      </div>


    <% end %>

    <% unless @record.summary.blank? %>
      <div class="summary-content">
        <h2>Summary</h2>
        <%== markdowned @record.summary %>
      </div>
    <% end %>

    <% if @record.text_content.present? %>
      <div class="text-content">
        <% unless @record.is_text_content_only? %>
          <h2>Text Content</h2>
        <% end %>
        <%== markdowned md_todo_replaced @record.text_content %>
      </div>
    <% end %>

    <% unless @record.source_content.blank? %>
      <% if @record.only_source_content? %>
        <h2>Source Content</h2>
        <div class="source-content">
          <%== markdowned @record.source_content %>
        </div>
      <% else %>
        <details>
          <summary>Source Content</summary>
          <div class="source-content">
            <%== markdowned @record.source_content %>
          </div>
        </details>
      <% end %>
    <% end %>
  <% end %>

  <% if @record.attachments.count > 0 %>
    <div>
      <%= render 'records/attachment_list', record: @record %>
    </div>
  <% end %>

  <p class="hide-until-details-checked">
    <%= link_to "Edit", edit_record_path(@record), class: "secondary button" %>
    &nbsp;
    mark:
    <%= link_to "read", mark_readed_record_path(@record) if @record.readed == false %>
    <%= link_to "unread", mark_not_readed_record_path(@record) if @record.readed %>
    <%= link_to "pinned", mark_pinned_record_path(@record) if @record.is_pinned == false %>
    <%= link_to "unpinned", mark_unpinned_record_path(@record) if @record.is_pinned %>
    <%= link_to "nice to have", mark_nice_to_have_record_path(@record) if @record.is_nice_to_have == false %>
    <%= link_to "not nice to have", mark_not_nice_to_have_record_path(@record) if @record.is_nice_to_have %>
    <%= link_to "archived", mark_archived_record_path(@record) if @record.is_archived == false %>
    <%= link_to "not archived", mark_not_archived_record_path(@record) if @record.is_archived %>

  </p>

  <% cache ["record actions", @record] do %>
    <div class="hide-until-details-checked">
      <details>
        <summary>Delete</summary>
        <%= button_to "Destroy this record", @record, method: :delete %>
      </details>
    </div>
  <% end %>


  <% if @record.has_resources? %>
    <div>
      <h3>Resources</h3>
      <p>
        <%= render 'records/resources_table', records: @record.children.where(kind: "resource") %>
      </p>
      <%= link_to "Add a resource", new_record_path(parent_id: @record.id, kind: "resource") %>
    </div>
  <% end %>

  <% if @record.has_rows? %>
    <div>
      <h3>Rows</h3>
      <p>
        <%= render 'records/rows_table', records: @record.children.where(kind: "row") %>
      </p>
      <%= link_to "Add a row", new_record_path(parent_id: @record.id, kind: "row") %>
    </div>
  <% end %>

  <div class="hide-until-details-checked">
    <% if @record.webpages.any? %>
      <div>
        <h3>Webpages</h3>
        <p>
          <%= render 'webpages/list', webpages: @record.webpages %>
        </p>
        <%= link_to "Add a webpage", new_webpage_path(record_id: @record.id) %>
      </div>
    <% end %>

    <h3>Child Records</h3>
    <p>
      <% if @record.children.any? %>
        <%= link_to "View in grid", records_path(id: @record.id) %>
      <% end %>

      <%= render 'records/children_list', record: @record %>

      <% if @record.is_todo? %>
        <%= link_to "Add a child todo", new_record_path(parent_id: @record.id, kind: "todo") %>
      <% else %>
        <%= link_to "Add a child record", new_record_path(parent_id: @record.id) %>

        <% if @record.is_people? %>
          <%= link_to "Add a follow-up", new_record_path(parent_id: @record.id, kind: "atomy follow-up") %>
          <%= link_to "Add a touch", new_record_path(parent_id: @record.id, kind: "row") %>
        <% end %>
      <% end %>

      <% if @record.parent %>
        <%= link_to "Add next record", new_record_path(parent_id: @record.parent.id, kind: @record.kind) %>
      <% end %>
    </p>

  </div>

  <div class="follow-up-remark">
    <% if @record.follow_up_remark.present? %>
      <h2>Follow-up remark</h2>
      <div>
        <%== markdowned @record.follow_up_remark %>
      </div>
    <% end %>
  </div>
  <details class="hide-until-details-checked">
    <summary>Follow-up</summary>
    <%= form_with(model: @record) do |form| %>
      <%= form.text_area :follow_up_remark %>
      <%= form.submit "Remark" %>
    <% end %>
  </details>


  <div class="hide-until-details-checked">
    <hr>
    <h2 id="ai-chats">AI Chat</h2>
    <% @record.chat_messages.each do |chat_message| %>
      <% next if chat_message.new_record? %>

      <div>
        <% if chat_message.role == "user" %>
          <p><strong>Makzan</strong></p>
          <div class='user-chat-message'>
            <%== markdowned chat_message.content %>
          </div>
        <% else %>
          <p><strong>AI Assistant</strong></p>
          <div class='assistant-chat-message'>
            <%== markdowned chat_message.content %>
          </div>
        <% end %>


      </div>
    <% end %>
    <details>
      <summary>Ask AI</summary>
      <%= form_with(model: @chat_message) do |form| %>
        <%= form.hidden_field :record_id, value: @record.id %>
        <%= form.hidden_field :role, value: "user" %>
        <%= form.text_area :content %>
        <%= form.submit "Ask AI" %>
      <% end %>
    </details>
  </div>

</div>
