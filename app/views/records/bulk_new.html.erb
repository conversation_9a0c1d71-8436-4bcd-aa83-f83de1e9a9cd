<div class="container wide">
  <h1>Bulk Upload Files</h1>

  <p>Upload multiple files to create one record per file. Each file will be attached to its own record.</p>

  <%= form_with url: bulk_create_records_path, local: true, multipart: true do |form| %>
    <div>
      <%= form.label :attachments, "Select Multiple Files", style: "display: block" %>
      <%= form.file_field :attachments, multiple: true, required: true, accept: "image/*,audio/*,video/*,.pdf,.doc,.docx,.txt" %>
      <small>You can select multiple files at once. Supported formats: images, audio, video, PDF, documents, text files.</small>
    </div>

    <details>
      <summary>Optional: Set Common Properties for All Records</summary>

      <div>
        <%= form.label :parent_id, "Parent Record ID", style: "display: block" %>
        <%= form.text_field :parent_id, value: @record.parent_id %>
        <small>If specified, all created records will be children of this record.</small>
      </div>

      <div>
        <%= form.label :project_id, "Project ID", style: "display: block" %>
        <%= form.text_field :project_id, value: @record.project_id %>
      </div>

      <div>
        <%= form.label :kind, "Record Type", style: "display: block" %>
        <%= form.select :kind, ["","todo", "event", "atomy follow-up", "people", "book", "quote", "journal", "row", "resource"], { selected: @record.kind }, { include_blank: true } %>
      </div>

      <div>
        <%= form.label :tagss, "Tags (comma-separated)", style: "display: block" %>
        <%= form.text_field :tagss %>
        <small>These tags will be applied to all created records.</small>
      </div>

      <div>
        <%= form.label :color, "Color", style: "display: block" %>
        <%= form.select :color, Record.color_options, { include_blank: true }, {} %>
      </div>

      <div>
        <%= form.label :text_content, "Common Text Content", style: "display: block" %>
        <%= form.text_area :text_content, rows: 3 %>
        <small>This text will be added to all created records.</small>
      </div>

      <div>
        <%= form.label :frontmatters, "Common Front Matter", style: "display: block" %>
        <%= form.text_area :frontmatters, rows: 3 %>
      </div>

      <div>
        <%= form.label :is_pinned, "Pin all records", style: "display: block" %>
        <%= form.check_box :is_pinned %>
      </div>

      <div>
        <%= form.label :is_nice_to_have, "Mark as nice to have", style: "display: block" %>
        <%= form.check_box :is_nice_to_have %>
      </div>

      <div>
        <%= form.label :is_archived, "Archive all records", style: "display: block" %>
        <%= form.check_box :is_archived %>
      </div>
    </details>

    <p>&nbsp;</p>

    <div>
      <%= form.submit "Create Records from Files", class: "button" %>
    </div>
  <% end %>

  <br>

  <div>
    <%= link_to "Back to records", records_path %>
    |
    <%= link_to "Regular new record", new_record_path %>
  </div>

  <script>
  document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.querySelector('input[type="file"]');
    const submitButton = document.querySelector('input[type="submit"]');

    fileInput.addEventListener('change', function() {
      const fileCount = this.files.length;
      if (fileCount > 0) {
        submitButton.value = `Create ${fileCount} Record${fileCount > 1 ? 's' : ''} from File${fileCount > 1 ? 's' : ''}`;
      } else {
        submitButton.value = 'Create Records from Files';
      }
    });
  });
  </script>
</div>