

<p>
<%= render "kinds_nav" %>
</p>

<p class="container text-center">
<% cache ["color coded dots", @color_coded_records_count] do %>
  <% Record.color_options.each do |color| %>
    <% if Record.where(color: color).count > 0 %>
      <%= link_to records_path(color: color) do %>
        <span class="color-dot" style="background-color: <%= color %>"></span>
      <% end %>
    <% end %>
  <% end %>
<% end %>
</p>

<nav class="container text-center minor-bottom-margin">
  <%= link_to "today", records_path({date: Date.current}) %>
  <%= link_to "chats", chat_messages_path %>
  <%= link_to "parents", records_path({has: "children"}) %>
  <br>
  <%= link_to "archived", records_path({archived: "true"}) %>
  <%= link_to "is:read", records_path({is_read: "true"}) %>
  <%= link_to "is:unread", records_path({is_read: "false"}) %>
  <br>
  <%= link_to "has:text", records_path({has: "text"}) %>
  <%= link_to "has:highlight", records_path({has: "highlight"}) %>
  <%= link_to "has:pdf", records_path({query: ".pdf"}) %>
  <%= link_to "has:audio", records_path({query: ".audio"}) %>
  <%= link_to "has:url", records_path({query: ".url"}) %>
  <br>
  <%= link_to "has:youtube", records_path({query: "youtu", search_for: "url"}) %>
  <%= link_to "no-captions", records_path({query: "No captions available"}) %>
  <%= link_to "has:bilibili", records_path({query: "b23.tv", search_for: "url"}) %>
  <%= link_to "has:frontmatter", records_path({query: ".frontmatter"}) %>

  <%= link_to "🎲", records_path({random: "true"}) %>

  <%= link_to "Bulk New", bulk_new_records_path %>
</nav>

<% if @show_calendar %>
  <% number_of_weeks = 1 %>
  <% number_of_weeks = 2 if Date.today.friday? or Date.today.saturday? or Date.today.sunday? %>
  <%= week_calendar(number_of_weeks: number_of_weeks) do |date| %>
    <%= render "records/day_cell", date: date %>
  <% end %>
<% end %>

<% if params[:query] %>

  <details>
    <summary>Refine Search</summary>

    <form action="/records" method="get">
      <label>Query
        <input type="text" name="query" value="<%= params[:query] %>">
      </label>
      <label>Tags
        <input type="text" name="tags" value="<%= params[:tags] %>">
      </label>
      <input type="hidden" name="search_for" value="title">
      <button type="submit">Search</button>
    </form>
    <p>&nbsp;</p>

  </details>

  <h1>
    Search results for "<%= params[:query] %>"
    <br>
    <% unless params[:tags].blank? %>
      #<%= params[:tags].split(",").map(&:strip).join(" #") %>
    <% end %>
    <% if params[:search_for] %>
      (search for <%= params[:search_for] %>)
    <% end %>
  </h1>
  <p>
    <%= link_to "Search all fields", records_path(query: params[:query]) %>

    <%= link_to "Search for URL", records_path(query: params[:query], search_for: "url") %>

    <%= link_to "Search for Frontmatter", records_path(query: params[:query], search_for: "frontmatter") %>

    <%= link_to "Search for Title", records_path(query: params[:query], search_for: "title") %>
  </p>


<% end %>

<% if params[:random] %>
  <p style="padding: 0.5em 0">
    <%= link_to "random", records_path({random: params[:random]}), class: "button" %>
  </p>
<% end %>

<% if params[:date] %>
  <% d = params[:date].to_date %>
  <p>
    <%= link_to "← #{d-1}", records_path({date: d-1}) %> |
    <%= link_to d, records_path({date: d}) %> |
    <%= link_to "#{d+1} →", records_path({date: d+1}) %>
  </p>
<% end %>

<!-- <div id="records">
  <% @records.each do |record| %>
    <%= render record, cached: true %>
  <% end %>
</div> -->

<% if @pinned_records && @pinned_records.any? %>
  <h2>Pinned</h2>
  <%= render "grid", records: @pinned_records %>

  <h2>Records</h2>
<% end %>

<%= render "grid", records: @records %>

<a name="more"></a>
<p> <%= link_to "More...", records_path(limit: @limit*2, anchor: "more", is_read: params[:is_read], archived: params[:archived], random: params[:random], date: params[:date], query: params[:query], tags: params[:tags], search_for: params[:search_for], has: params[:has]) %> </p>

<details>
  <summary>Quick assign tags to these <%= @records.count %> records</summary>
  <%= form_with url: bulk_set_tags_records_path, method: :post, local: true do |form| %>
    <%= form.hidden_field :query, value: params[:query] %>
    <%= form.hidden_field :record_ids, value: @records.map(&:id).join(",") %>
    <%= form.label :tagss, style: "display: block" %>
    <%= form.text_field :tagss %>
    <%= form.submit %>
  <% end %>
</details>

<% unless @attachments.blank? %>
  <h2>Attachments matches</h2>
  <div class="grid">
      <% @attachments.each do |a| %>
          <% cache [a] do %>
              <figure>
                  <%= link_to attachment_path(a.key) do %>
                      <%= image_tag a.preview(resize_to_limit: [200, 200]) if a.previewable? %>
                      <%= image_tag a.variant(resize_to_limit: [200, 200]) if a.variable? %>
                  <% end %>
                  <figcaption>
                      <%= link_to a.record.title, a.record %><br>
                  </figcaption>
              </figure>
          <% end %>
      <% end %>
  </div>
<% end %>


<% unless @records2.blank? %>
  <h2>Filename matches</h2>
  <%= render "grid", records: @records2 %>
<% end %>

<% unless @records3.blank? %>
  <h2>Chat messages matches</h2>
  <%= render "grid", records: @records3 %>
<% end %>

