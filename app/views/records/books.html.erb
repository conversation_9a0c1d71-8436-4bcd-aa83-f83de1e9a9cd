<div class="container wide">
    <h1>
        Books
        <% if params[:query].present? %>
            <small>(<%= params[:query] %>)</small>
        <% end %>
    </h1>

    <p>
        <%= link_to "New Book", new_record_path(kind: "book"), class: "button" %>

        <%= link_to "Random Clips", records_path({random: "book_clips"}) %>
    </p>

    <p>
        <%= link_to "Shelf", books_records_path(view: "shelf") %>

        <%= link_to "Grid", books_records_path(view: "grid") %>

        <%= link_to "List", books_records_path(view: "list") %>
    </p>

    <%= form_with url: books_records_path, method: :get do |f| %>
        <%= f.label :query %>
        <%= f.text_field :query, value: params[:query] %>
    <% end %>

    <% if @view == "shelf" %>
        <%= render "books_shelf", records: @records %>
    <% elsif @view == "list" %>
        <%= render "book_list", records: @records %>
    <% else %>
        <%= render "grid", records: @records %>
    <% end %>
</div>
