<%= form_with(model: record) do |form| %>
  <% if record.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(record.errors.count, "error") %> prohibited this record from being saved:</h2>

      <ul>
        <% record.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :title, style: "display: block" %>
    <%= form.text_field :title, autofocus: (@record.kind == "todo" or @record.kind == "people" or @record.kind == "atomy follow-up" or @record.kind == "row") %>
  </div>

  <details>
    <summary>Meta</summary>

    <div>
      <%= form.label :parent_id, "Parent Record", style: "display: block" %>
      <%= form.text_field :parent_id %>
    </div>

    <div>
      <%= form.label :project_id, style: "display: block" %>
      <%= form.text_field :project_id %>
    </div>

    <div>
      <%= form.label :tagss, style: "display: block" %>
      <%= form.text_field :tagss %>

      <details>
        <summary>Tags</summary>
        <% Tag.all.order(name: :asc).each do |tag| %>
          <%= tag.name %>
        <% end %>
      </details>
    </div>

    <div>
      <%= form.label :color, style: "display: block" %>
      <%= form.select :color, Record.color_options, { include_blank: true }, {} %>
    </div>

    <div>
      <%= form.label :frontmatters, style: "display: block" %>
      <%= form.text_area :frontmatters %>
    </div>

    <div>
      <%= form.label :url, style: "display: block" %>
      <%= form.text_field :url %>
    </div>

    <div>
      <%= form.label :kind, style: "display: block" %>
      <%= form.select :kind, ["","todo", "event", "atomy follow-up", "people", "book", "quote", "journal", "row", "resource"], {}, { multiple: false } %>
    </div>

    <div>
      <%= form.label :date_at, style: "display: block" %>
      <%= form.date_field :date_at %>
    </div>

    <div>
      <%= form.label :is_pinned, style: "display: block" %>
      <%= form.check_box :is_pinned %>
    </div>

    <div>
      <%= form.label :is_nice_to_have, style: "display: block" %>
      <%= form.check_box :is_nice_to_have %>
    </div>

    <div>
      <%= form.label :is_archived, style: "display: block" %>
      <%= form.check_box :is_archived %>
    </div>



  </details>

  <div>
    <%= form.file_field :attachments, multiple: true %>
    <% if record.attachments.attached? %>
      <% record.attachments.each do |attachment| %>
        <%= form.hidden_field :attachments, value: attachment.signed_id, multiple: true %>
        <%= image_tag attachment.preview(resize_to_limit: [100, 100]) if attachment.previewable? %>
        <%= image_tag attachment.variant(resize_to_limit: [100, 100]) if attachment.variable? %>
        <p>Attached file: <%= attachment.filename %></p>
      <% end %>
    <% end %>
  </div>

  <div class="text-content">
    <%= form.label :text_content, style: "display: block" %>
    <%= form.text_area :text_content, autofocus: (@record.kind != "todo" and @record.kind != "people" and @record.kind != "atomy follow-up" and @record.kind != "row") %>
  </div>

  <div>
    <%= form.label :generated_content, style: "display: block" %>
    <%= form.text_area :generated_content %>
  </div>

  <div>
    <%= form.label :source_content, style: "display: block" %>
    <%= form.text_area :source_content %>
  </div>


  <details>
    <summary>AI Helper</summary>
    <div>
      <%= form.label :ai_model, style: "display: block" %>
      <%= form.collection_select :ai_model, AiModel.all, :to_s, :to_s, { include_blank: true }, { data: { list: "ai_models" } } %>
    </div>

    <div>
      <%= form.label :system_prompt_id, style: "display: block" %>
      <%= form.collection_select :system_prompt_id, SystemPrompt.all, :id, :to_s, { include_blank: true }, { data: { list: "system_prompts" } } %>
    </div>

    <div>
      <%= form.label :prompt, style: "display: block" %>
      <%= form.text_area :prompt %>
    </div>

    <div>
      <%= form.label :ai_process_status, style: "display: block" %>
      <%= form.text_field :ai_process_status %>
    </div>
  </details>

  <details>
    <summary>Advanced</summary>


    <div>
      <%= form.label :summary, style: "display: block" %>
      <%= form.text_area :summary %>
    </div>

    <div>
      <%= form.label :highlighted_text, style: "display: block" %>
      <%= form.text_area :highlighted_text %>
    </div>

    <% unless record.new_record? %>
      <div>
        <%= link_to "Clear and Refetch URL", record_clear_and_refetch_url_path(record), class: "button" %>
      </div>
    <% end %>


  </details>

  <p>&nbsp;</p>

  <div class="fixed-top-right">
    <%= form.submit %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
