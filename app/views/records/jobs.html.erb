<h2>Jobs</h2>
<p><%= @records.count %> jobs waiting.</p>

<p><%= link_to "Run Jobs", jobs_running_records_path %></p>

<iframe src="/records/jobs_running"></iframe>

<table>
  <tr>
    <th>ID</th>
    <th>Title</th>
    <th>URL</th>
    <th>Status</th>
  </tr>
<% @records.each do |record| %>
  <tr>
    <td><%= link_to record.id, record %></td>
    <td><%= record.title %></td>
    <td><%= record.url %></td>
    <td><%= record.ai_process_status %></td>
  </tr>
<% end %>
</table>



<script>

  // fetch /records/jobs.json
  const interval = setInterval(() => {
    fetch("/records/jobs.json").then(response => response.json()).then(data => {
      if (data > 0) {
        clearInterval(interval);
        window.location.reload();
      }
    });
  }, 30 * 1000);
</script>
