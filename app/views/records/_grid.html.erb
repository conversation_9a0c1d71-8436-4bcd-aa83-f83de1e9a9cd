<div class="records-grid">

  <% records.each do |record| %>
    <% if record.is_a?(Record) %>
        <% cache [record] do %>
        <div class="record-card" style="border-top: 3px solid <%= record.color_code %>">
            <div class="bottom-right">
                <%= link_to record.id, record %>
            </div>

            <div class="bottom-left">
                <% if record.url.present? %>
                    <span class="record-card-url"><%= link_to record.url, record.url %></span>
                <% end %>
            </div>

            <% if record.is_todo? %>
                <span class="todo">
                <% if record.is_done? %>
                    ✅
                <% else %>
                    ⬜️
                <% end %>
                </span>
            <% end %>

            <% if record.is_people? %>
                <span class="people">
                👤
                </span>
            <% end %>

            <% if record.is_book? %>
                <span class="book">
                📚
                </span>
            <% end %>

            <% if record.is_journal? %>
                <span class="journal">
                🌱
                </span>
            <% end %>

            <% if record.is_youtube? || record.is_bilibili? %>
                <span class="youtube">
                ⏯️
                </span>
            <% end %>

            <%= link_to record.title, record %>

            <% if record.parent %>
                <div>
                    <small>
                      <%= link_to "↰ #{record.parent.title}", record.parent %>
                    </small>
                </div>
            <% end %>

            <div class="tags-list">
                <% record.tags.each do |tag| %>
                    <%= link_to "##{tag.name}", tag %>
                <% end %>
            </div>

            <% if record.attachments.attached? %>
              <% if record.attachments.first.image? %>
                  <%= image_tag record.attachments.first.variant(resize_to_limit: [250,250]) %>
              <% else %>
                  <small>📎 <%= record.attachments.first.filename %></small>
              <% end %>
            <% end %>

            <div class='card-text-content' style="zoom:0.5;">
              <% unless record.highlighted_text.blank? %>
                <div style="border-left: 4px solid gold;padding-left:0.5em;">
                  <%== markdowned record.highlighted_text %>
                </div>
              <% end %>
              <%== markdowned record.summary %>
              <%== markdowned md_todo_replaced record.text_content %>
              <%== markdowned record.generated_content %>
            </div>
        </div>
        <% end %>
    <% end %>
  <% end %>
</div>