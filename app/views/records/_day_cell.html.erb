<div class='date'>
    <%= link_to date, daily_checkings_path(date: date) %>
    <% journal = @journals.find_by(date_at: date) %>
    <%= link_to "🌱", journal if journal %>
    <small><strong>
        <%= DailyChecking.title_of_the_day(date) %>
    </strong></small>
</div>

<% @todos.where(date_at: date).sort_by(&:time_begin_at).each do |todo| %>
    <% unless todo.is_done? %>
    <div>
        <span class='time'>
        <% if todo.is_done? %>
            ✅
        <% else %>
            ⬜️
        <% end %>
        </span>
        <%= link_to todo.title, todo %>
    </div>
    <% end %>
<% end %>

<% @events.where(date_at: date).sort_by(&:time_begin_at).each do |event| %>
    <div>
    <span class='time'><%= event.time_begin_at %></span>
    <%= link_to event.title, event %>
    </div>
<% end %>

<%= link_to "+", new_record_path(date_at: date, kind: "event") %>
