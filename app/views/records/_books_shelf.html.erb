<div class="books-shelf">
    <% records.each do |record| %>
        <%= link_to record do %>
            <div class="book">
                <div class="book-cover">
                    <% if record.attachments.attached? %>
                        <% if record.attachments.first.previewable? %>
                            <%= image_tag record.attachments.first.preview(resize_to_limit: [200, 200]) %>
                        <% elsif record.attachments.first.variable? %>
                            <%= image_tag record.attachments.first.variant(resize_to_limit: [200, 200]) %>
                        <% end %>
                    <% end %>
                </div>
                <%
                    if record.title =~ /\p{Han}/
                        class_name = "chinese-title"
                    else
                        class_name = "english-title"
                    end
                %>
                <div class="book-info <%= class_name %>">
                    <h2><%= record.title %></h2>
                    <span class="book-children-count"><%= record.children.count %></span>
                </div>
            </div>
        <% end %>
    <% end %>
</div>