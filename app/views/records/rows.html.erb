<div class="container wide">
    <%= render "kinds_nav" %>

    <h1>Rows</h1>

    <!-- <p><%= link_to "New Row", new_record_path(kind: "row"), class: "button" %></p> -->

    <h2>Parents</h2>
    <ul>
        <% @parents.select(:kind).map(&:kind).uniq.sort.each do |p| %>
            <% if p == "" %>
                <% @parents.where(kind: p).each do |parent| %>
                    <li><%= link_to parent, parent %></li>
                <% end %>
            <% else %>
                <li>
                    <strong><%= p%></strong>
                    <ul>
                        <% @parents.where(kind: p).each do |parent| %>
                            <li><%= link_to parent, parent %></li>
                        <% end %>
                    </ul>
                </li>
            <% end %>
        <% end %>
    </ul>

    <h2>All Rows</h2>

    <%= render "rows_table", records: @records %>


</div>
