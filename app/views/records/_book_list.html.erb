<table class="lined-table">
    <% records.each do |record| %>
        <tr>
            <td><%= link_to record.title, record %></td>
            <td>
                <% if record.attachments.attached? %>
                    <% if record.attachments.first.previewable? %>
                        <%= image_tag record.attachments.first.preview(resize_to_limit: [100, 100]) %>
                    <% elsif record.attachments.first.variable? %>
                        <%= image_tag record.attachments.first.variant(resize_to_limit: [100, 100]) %>
                    <% end %>
                <% end %>
            </td>
            <td><%= record.children.count %></td>
        </tr>
    <% end %>
</table>
