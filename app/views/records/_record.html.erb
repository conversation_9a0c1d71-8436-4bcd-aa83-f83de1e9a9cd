<% cache ["record list", record] do %>
  <div id="<%= dom_id record %>">
    <p>
      <%= link_to record.id, record %>

      <% if record.parent %>
        <%= link_to "↰", record.parent, title: record.parent.title %>
      <% end %>

      <% if record.is_todo? %>
        <span class="todo">
          <% if record.is_done? %>
            ✅
          <% else %>
            ⬜️
          <% end %>
        </span>
      <% end %>

      <% if record.is_people? %>
        <span class="people">
          👤
        </span>
      <% end %>

      <% if record.is_book? %>
        <span class="book">
          📚
        </span>
      <% end %>

      <% if record.is_journal? %>
        <span class="journal">
          🌱
        </span>
      <% end %>

      <span><%= record.date_at %></span>

      <span>
        <% record.tags.each do |tag| %>
          <%= link_to "##{tag.name}", tag %>
        <% end %>
      </span>

      <span><%= record.title %></span>

      <% if record.attachments.attached? %>
        <% if record.attachments.first.image? %>
          <%= image_tag record.attachments.first.variant(resize_to_limit: [50,50]) %>
        <% else %>
          📎 <%= record.attachments.first.filename %>
        <% end %>
      <% end %>

      <%= link_to record.url, record.url %>

      <%= record.text_content && record.text_content[0..30] %>
    </p>


  </div>
<% end %>
