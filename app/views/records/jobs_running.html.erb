<h2>Jobs Running</h2>
<p><%= @records.count %> jobs waiting.</p>
<table>
<% @records.each do |record| %>
  <tr>
    <td><%= record.id %></td>
    <td><%= record.title %></td>
    <td><%= record.url %></td>
    <td><%= record.ai_process_status %></td>
  </tr>
<% end %>
</table>

<script>
  // // fetch /records/jobs.json
  // const interval = setInterval(() => {
  //   fetch("/records/jobs.json").then(response => response.json()).then(data => {
  //     if (data > 0) {
  //       clearInterval(interval);
  //       window.location.reload();
  //     }
  //   });
  // }, 15 * 1000);
</script>