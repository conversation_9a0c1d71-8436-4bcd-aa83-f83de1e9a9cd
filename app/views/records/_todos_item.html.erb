<% cache ["record todo item", record, Date.current] do %>
    <% if record.is_done? %>
        <%= "✅ #{record.updated_at.to_date}"  %>
    <% else %>
        <%= link_to "⬜️", record_mark_done_path(record) unless record.is_done? %>
    <% end %>
    <% if record.parent %>
        <%= link_to "↰", record.parent, title: record.parent.title %>
    <% end %>

    <strong>
        <%= record.date_at %>
        <% unless record.date_at.blank? %>
            <% if record.date_at > Date.today %>
                <small class='future-date'>(in <%= (record.date_at - Date.today).to_i %> days)</small>
            <% end %>
            <% if record.date_at < Date.today %>
                <small class='past-date'>(<%= (Date.today - record.date_at).to_i %> days ago)</small>
            <% end %>
        <% end %>
    </strong>

    <% record.tags.each do |tag| %>
        <%= link_to "##{tag.name}", todos_records_path(tag: tag.name) %>
    <% end %>

    <%= link_to record.title, record, style: "color: #305a82; font-size:1em;" %>
<% end %>