<table class="lined-table">
    <thead>
        <tr>
            <th>Record</th>
            <th>Content</th>
        </tr>
    </thead>
    <% records.each do |record| %>
        <tr>
            <td>
                <% if record.title.present? %>
                    <%= link_to record.title, record %>
                <% else %>
                    <%= link_to record.id, record %>
                <% end %>
            </td>
            <!-- <td><%= record.date_at %></td> -->
            <!-- <td><%= record.tagss %></td> -->
            <td>
                <% if record.text_content.length > 200 %>
                    <%= record.text_content[0..200] %>
                <% else %>
                    <%== markdowned record.text_content %>
                <% end %>
            </td>
        </tr>
    <% end %>
</table>