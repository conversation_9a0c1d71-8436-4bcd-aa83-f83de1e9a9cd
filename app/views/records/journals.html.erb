<div class="container">
  <h1>Journals</h1>

  <p>
    <%= link_to "New Journal", new_record_path(kind: "journal"), class: "button" %>
  </p>

  <% @records.each do |record| %>
    <div class="google-card">
      <h2><%= link_to record.date_at, record %></h2>
      <p>
        <strong><%= link_to record.title, record %></strong>
      </p>

      <% if record.attachments.count == 1 %>
        <% a = record.attachments.first %>
        <% cache [a,'500'] do %>
              <figure>
                  <%= link_to attachment_path(a.key) do %>
                      <%= image_tag a.preview(resize_to_limit: [500, 500]) if a.previewable? %>
                      <%= image_tag a.variant(resize_to_limit: [500, 500]) if a.variable? %>
                  <% end %>
                  <figcaption>
                      <%= link_to a.record.title, a.record %>
                      <small><%= link_to "(#{number_to_human_size a.blob.byte_size})", attachment_path(a.key) %></small>
                  </figcaption>
              </figure>
          <% end %>
      <% elsif record.attachments.count > 1 %>
        <div class='row'>
        <% record.attachments.each do |a| %>
          <% cache [a,'200'] do %>
                <figure>
                    <%= link_to attachment_path(a.key) do %>
                        <%= image_tag a.preview(resize_to_limit: [200, 200]) if a.previewable? %>
                        <%= image_tag a.variant(resize_to_limit: [200, 200]) if a.variable? %>
                    <% end %>
                    <figcaption>
                        <%= link_to a.record.title, a.record %>
                        <small><%= link_to "(#{number_to_human_size a.blob.byte_size})", attachment_path(a.key) %></small>
                    </figcaption>
                </figure>
            <% end %>
          <% end %>
          </div>
      <% end %>

      <p><%== markdowned record.text_content %></p>

      <p><small><%= link_to record.id, record %></small></p>
    </div>
  <% end %>
</div>