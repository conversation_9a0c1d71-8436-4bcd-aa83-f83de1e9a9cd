<div class="container wide">
    <h1>
        To-Dos
        <% if params[:tag].present? %>
            <small>(#<%= params[:tag] %>)</small>
        <% end %>
    </h1>
    <nav>
        <%= link_to "To-do", todos_records_path %>
        <%= link_to "Done", done_todos_records_path %>
        <%= link_to "✨ AI Assist", todos_ai_records_path %>
    </nav>

    <p><%= link_to "New TODO", new_record_path(kind: "todo"), class: "button" %></p>

    <p>
        <% @tags.each do |tag| %>
            <%= link_to "##{tag}", todos_records_path(tag: tag) %>
        <% end %>
    </p>


    <table>
        <thead>
            <tr>
                <th colspan="3">Tasks</th>
            </tr>
        </thead>

        <% titles = ["Pinned", "Today", "Past", "Future", "Other"] %>
        <% [@pinned_todos, @today_todos, @past_todos, @future_todos, @other_todos].each_with_index do |records, index| %>
            <% if records.any? %>
                <tr>
                    <th colspan="3" style="text-align: left;"><%= titles[index] %></th>
                </tr>
            <% end %>
            <% records.each do |record| %>
                <% unless record.is_done? %>
                    <%= render "todos_row", record: record %>
                <% end %>
            <% end %>
        <% end %>
    </table>
</div>