<% if record.attachments.count > 0 %>
  <h2>Attachments</h2>
<% end %>
<% record.attachments.each do |a| %>
  <div class="grid-card">
    <% if a.image? %>
      <figure>
        <%= image_tag a.variant(resize_to_limit: [640, 640]), width: 320 if a.variable? %>
        <figcaption>
          <small>
            <%= link_to a.filename, a  %>
            <%= link_to "(#{number_to_human_size a.blob.byte_size})", attachment_path(a.key) %>
          </small>
        </figcaption>
      </figure>
      <div>
        <strong>OCR:</strong>
        <%== markdowned a.custom_metadata && a.custom_metadata["ocr_content"] %>
      </div>
    <% elsif a.previewable? %>
      <figure>
        <%= image_tag a.preview(resize_to_limit: [640, 640]), width: 320 %>
        <figcaption>
          <small>
            <%= link_to a.filename, a %>
            <%= link_to "(#{number_to_human_size a.blob.byte_size})", attachment_path(a.key) %>
          </small>
        </figcaption>
      </figure>
      <div>
        <strong>OCR:</strong>
        <%== markdowned a.custom_metadata && a.custom_metadata["ocr_content"] %>
      </div>
    <% elsif a.audio? %>
      <audio controls>
        <source src="<%= url_for a %>" type="<%= a.content_type %>">
        Your browser does not support the audio element.
      </audio>
    <% else %>
      📦 <%= link_to a.filename, a %>
      <%= link_to "(#{number_to_human_size a.blob.byte_size})", attachment_path(a.key) %>
    <% end %>


  </div>
<% end %>
