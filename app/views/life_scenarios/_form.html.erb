<%= image_tag "life_scenarios.jpeg" %>

<table>
  <thead>
    <tr>
      <td colspan="2" style="text-align: center">填寫備註</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="font-weight: bold">title</th>
      <td>填上四個字以內分類</td>
    </tr>
    <tr>
    <th style="font-weight: bold">score</th>
    <td>填上 0-10 分數</td>
  </tr>
  <tr>
    <th style="font-weight: bold">remark</th>
    <td>夢想詳情、細節</td>
  </tr>
</table>

<%= form_with(model: life_scenario) do |form| %>
  <% if life_scenario.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(life_scenario.errors.count, "error") %> prohibited this life_scenario from being saved:</h2>

      <ul>
        <% life_scenario.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :written_at, style: "display: block" %>
    <%= form.date_field :written_at %>
  </div>

  <div>
    <%= form.label :preface, style: "display: block" %>
    <%= form.text_area :preface %>
  </div>

  <div>
    <%= form.label :title_1, style: "display: block" %>
    <%= form.text_field :title_1 %>
  </div>

  <div>
    <%= form.label :score_1, style: "display: block" %>
    <%= form.number_field :score_1 %>
  </div>

  <div>
    <%= form.label :remark_1, style: "display: block" %>
    <%= form.text_area :remark_1 %>
  </div>

  <div>
    <%= form.label :title_2, style: "display: block" %>
    <%= form.text_field :title_2 %>
  </div>

  <div>
    <%= form.label :score_2, style: "display: block" %>
    <%= form.number_field :score_2 %>
  </div>

  <div>
    <%= form.label :remark_2, style: "display: block" %>
    <%= form.text_area :remark_2 %>
  </div>

  <div>
    <%= form.label :title_3, style: "display: block" %>
    <%= form.text_field :title_3 %>
  </div>

  <div>
    <%= form.label :score_3, style: "display: block" %>
    <%= form.number_field :score_3 %>
  </div>

  <div>
    <%= form.label :remark_3, style: "display: block" %>
    <%= form.text_area :remark_3 %>
  </div>

  <div>
    <%= form.label :title_4, style: "display: block" %>
    <%= form.text_field :title_4 %>
  </div>

  <div>
    <%= form.label :score_4, style: "display: block" %>
    <%= form.number_field :score_4 %>
  </div>

  <div>
    <%= form.label :remark_4, style: "display: block" %>
    <%= form.text_area :remark_4 %>
  </div>

  <div>
    <%= form.label :title_5, style: "display: block" %>
    <%= form.text_field :title_5 %>
  </div>

  <div>
    <%= form.label :score_5, style: "display: block" %>
    <%= form.number_field :score_5 %>
  </div>

  <div>
    <%= form.label :remark_5, style: "display: block" %>
    <%= form.text_area :remark_5 %>
  </div>

  <div>
    <%= form.label :title_6, style: "display: block" %>
    <%= form.text_field :title_6 %>
  </div>

  <div>
    <%= form.label :score_6, style: "display: block" %>
    <%= form.number_field :score_6 %>
  </div>

  <div>
    <%= form.label :remark_6, style: "display: block" %>
    <%= form.text_area :remark_6 %>
  </div>

  <div>
    <%= form.label :title_7, style: "display: block" %>
    <%= form.text_field :title_7 %>
  </div>

  <div>
    <%= form.label :score_7, style: "display: block" %>
    <%= form.number_field :score_7 %>
  </div>

  <div>
    <%= form.label :remark_7, style: "display: block" %>
    <%= form.text_area :remark_7 %>
  </div>

  <div>
    <%= form.label :title_8, style: "display: block" %>
    <%= form.text_field :title_8 %>
  </div>

  <div>
    <%= form.label :score_8, style: "display: block" %>
    <%= form.number_field :score_8 %>
  </div>

  <div>
    <%= form.label :remark_8, style: "display: block" %>
    <%= form.text_area :remark_8 %>
  </div>

  <div>
    <%= form.label :postface, style: "display: block" %>
    <%= form.text_area :postface %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
