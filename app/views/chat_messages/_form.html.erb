<%= form_with(model: chat_message) do |form| %>
  <% if chat_message.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(chat_message.errors.count, "error") %> prohibited this chat_message from being saved:</h2>

      <ul>
        <% chat_message.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :record_id, style: "display: block" %>
    <%= form.text_field :record_id %>
  </div>

  <div>
    <%= form.label :content, style: "display: block" %>
    <%= form.text_area :content %>
  </div>

  <div>
    <%= form.label :role, style: "display: block" %>
    <%= form.text_field :role %>
  </div>

  <div>
    <%= form.label :ai_model_slug, style: "display: block" %>
    <%= form.text_field :ai_model_slug %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
