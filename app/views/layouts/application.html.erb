<!DOCTYPE html>
<html>
  <head>
    <title>MZ Base</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body>
    <% if user_signed_in? %>
      <nav class="container" style="text-align: center">
        <div class="xxhide-in-mobile">
          <%= link_to "🏠", root_path %>
          <br>
          <%= link_to "📝", new_record_path %>
          <%= link_to "🔍", search_path %>
          <br>
          <%= link_to "☑️", new_record_path(kind: "todo") %>
          <%= link_to "✅", todos_records_path %>
          <!-- <%= link_to "💬", atomy_follow_ups_records_path %> -->
          <%= link_to "🎯", projects_path %>
          <br>
          <%= link_to "👤", people_records_path %>
          <%= link_to "📚", books_records_path %>
          <%= link_to "🌱", journals_records_path %>
          <%= link_to "🎡", life_scenarios_path %>
          <br>
          <%= link_to "❝❞", quotes_records_path %>
          <%= link_to "🌐", webpages_path %>
          <%= link_to "=", "/records/rows" %>
          <%= link_to "📊", resources_records_path %>
          <%= link_to "📅", daily_checkings_path %>
        </div>

        <div class="show-only-in-mobile" style="display: none !important;">
          <ul class="mobile-nav">
            <li><%= link_to "🏠 Home", root_path %>
            <li><%= link_to "📝 +Record", new_record_path %>
            <li><%= link_to "📁 Bulk Upload", bulk_new_records_path %>
            <!-- <li><%= link_to "☑️ New Todo", new_record_path(kind: "todo") %> -->
            <li><%= link_to "🔍 Search", search_path %>
            <li><%= link_to "✅ Todos", todos_records_path %>
            <!-- <li><%= link_to "💬 Follow-up", atomy_follow_ups_records_path %> -->
            <li><%= link_to "👤 People", people_records_path %>
            <li><%= link_to "📚 Books", books_records_path %>
            <li><%= link_to "🌱 Journals", journals_records_path %>
            <li><%= link_to "❝❞ Quotes", quotes_records_path %>
            <li><%= link_to "📊 Resources", resources_records_path %>
            <li><%= link_to "📅 Checkings", daily_checkings_path %>
          </ul>
        </div>



        <details class="minor-top-margin">
          <summary>+Record</summary>
          <div class="record-card" style="border-top: 3px solid transparent;zoom:0.5;">
            <%= form_with(model: Record.new) do |form| %>
              <%= form.hidden_field :title, value: "" %>
              <%= form.text_area :text_content %>
              <%= form.submit %>
            <% end %>
          </div>
        </details>
      </nav>
    <% end %>

    <p class="notice-message"><%= notice %></p>

    <%= yield %>

    <footer class="container" style="text-align: center">
      <% if user_signed_in? %>
        <p>
          <%= link_to "Attachments", attachments_path %>
          <%= link_to "System Prompts", system_prompts_path %>
          <%= link_to "AI Usages", ai_usages_path %>
          <%= link_to "Providers", ai_providers_path %>
          <%= link_to "Models", ai_models_path %>
          <%= link_to "Jobs", jobs_records_path %>
          <%= link_to "Daily Statistics", daily_statistics_path %>
        </p>

        <p><%= link_to "Sign out", destroy_user_session_path, data: { "turbo-method": :delete } %></p>
      <% end %>
    </footer>
  </body>
</html>
