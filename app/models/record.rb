class Record < ApplicationRecord

  has_and_belongs_to_many :tags, join_table: "tags_records_relationships"

  belongs_to :parent, class_name: "Record", optional: true
  has_many :children, class_name: "Record", foreign_key: "parent_id", dependent: :nullify
  # nullify: Ensures that when a parent record is deleted, its children are not deleted but their parent_id is set to NULL.

  belongs_to :project, optional: true

  has_many_attached :attachments

  has_many :chat_messages, dependent: :destroy

  has_many :webpages, dependent: :nullify

  belongs_to :system_prompt, optional: true

  def to_s
    title
  end

  def self.search_for(query)
    where("title LIKE ? or text_content LIKE ? or source_content LIKE ? or summary LIKE ? or highlighted_text LIKE ? or frontmatters LIKE ? or url LIKE ?", "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%")
  end

  def self.search_for_title(query)
    where("title LIKE ?", "%#{query}%")
  end

  def self.search_for_url(query)
    where("url LIKE ?", "%#{query}%")
  end

  def self.search_for_frontmatter(query)
    where("frontmatters LIKE ?", "%#{query}%")
  end

  def self.date_at(date)
    where(
      "created_at BETWEEN :start AND :end OR updated_at BETWEEN :start AND :end OR date_at BETWEEN :start AND :end",
      start: date.beginning_of_day,
      end: date.end_of_day
    )
  end

  def self.created_at(date)
    where("created_at BETWEEN :start AND :end", start: date.beginning_of_day, end: date.end_of_day)
  end

  def self.updated_at(date)
    where("updated_at BETWEEN :start AND :end", start: date.beginning_of_day, end: date.end_of_day)
  end

  def self.created_until(date)
    where("created_at BETWEEN :start AND :end", start: "1970-01-01".to_date.beginning_of_day, end: date.end_of_day)
  end

  def self.todos
    where(kind: "todo")
  end

  def self.pinned
    where("is_pinned = true")
  end

  def self.not_pinned
    where("is_pinned IN (false, NULL)")
  end

  def self.nice_to_have_only
    where("is_nice_to_have = true")
  end

  def self.not_nice_to_have
    where("is_nice_to_have IN (false, NULL)")
  end

  def self.archived
    where("is_archived = true")
  end

  def self.not_archived
    where("is_archived IN (false, NULL)")
  end

  def self.is_read
    where("readed = true")
  end

  def self.is_unread
    where("readed IN (false, NULL)")
  end

  def self.ask_ai_about_todos(todos_content, done_count)
    Rails.cache.fetch([todos_content, done_count]) do
      ai_response = AiModel.ask_ai("今日是 #{Date.today}。今日已完成了 #{done_count} 件事項\n\n另外，我有以下待辦事項，你是我的高效助手及金句王，而我有拖延症，請幫我分析及分拆我如何有條不紊及有序、有心情地完成以下待辦事項。以繁體中文回覆：\n#{todos_content}")
      ai_response[:content].strip
    end
  end

  def self.color_options
    # ["gold", "orange", "red", "purple", "steelblue", "rgb(0,181,239)", "green"]
    ["red", "gold", "rgb(0,181,239)", "steelblue", "purple", "orange", "green"]
  end

  def self.colored_as_atomy
    where(color: "rgb(0,181,239)")
  end

  def self.colored_as_worldskills
    where(color: "purple")
  end

  def self.colored_as_cpttm
    where(color: "steelblue")
  end

  def is_text_content_only?
    text_content.present? && source_content.blank? && summary.blank? && generated_content.blank?
  end

  def color_code
    if self.color.blank?
      return "#ccc"
    end
    self.color
  end

  def save_or_update_tags!
    return if tagss.blank?
    # Save the tags relationship by splitting the "," and strip each one
    tag_names = tagss.split(",").map(&:strip)
    tags = []
    tag_names.each do |tag_name|
      # Convert to lowercase, replace spaces with underscores <del>, and keep special characters</del>
      final_tag_name = tag_name.downcase.gsub(/\s+/, '-') #.gsub(/[^\w\+@#%\$&\*!=\-?<>\[\]{}~.]/, '')
      tags << Tag.find_or_create_by(name: final_tag_name)
    end
    self.tags = tags
  end

  def auto_move_text_content_to_url_if_only_url!
    if url.blank? && text_content.present?
      # if text_content is only one line
      if text_content.strip.count("\n") == 0
        # if text_content is one line URL, move it to url
        if text_content.strip =~ /^https?:\/\//
          self.update(url: text_content.strip)
          self.update(text_content: "")
        end
      end
    end
  end

  def auto_ocr_all_attachments!
    attachments.each do |attachment|
      # if not yet set custom_metadata ocr_content
      if attachment.custom_metadata["ocr_content"].blank?
        if attachment.image?

          raw_content = ""
          # Extract the text only
          begin
            ai_response = AiModel.ask_ai_by_model_slug("請抽取此圖片中的所有文字。如果排板不是從上而下，可能是因為這是手寫筆記及寫作時沒有排版，請按上文下理修正次序及重組文字，但不要刪改原文意思。請直接回覆，不需要內容以外的客套說話。", "openrouter/gemini-flash", attachment.url)
            text = ai_response[:content].strip
            raw_content = text
          rescue => e
            raw_content = "Error: #{e.message}"
          end

          # Understand and summary the image
          begin
            ai_response = AiModel.ask_ai_by_model_slug("請辨認此圖片內容，如果是文字，這是我的手寫筆記，請翻譯成繁體中文。幫我重寫提供的文字，成為易讀及有條理的繁體中文文章。如果不是文字而是圖片，請返回最適當的描述，不用上下文，只需要直接回覆，以繁體中文撰寫", "openrouter/gemini-flash", attachment.url)
            alt = ai_response[:content].strip
            attachment.update(custom_metadata: { ocr_raw_content: raw_content, ocr_content: alt })
            attachment.save
          rescue => e
            attachment.update(custom_metadata: { ocr_raw_content: raw_content, ocr_content: "Error: #{e.message}" })
            attachment.save
          end
        end
      end
    end
  end

  # def fetch_youtube_html_from_url_through_fgc
  #   require 'httparty'
  #   response_html = HTTParty.get("https://www.cpttm.org.mo/gs1/fgc.php?url=#{url}")
  #   match_data = response_html.match /(\[{"baseUrl":.*"trackName":"(.*?)"}\])/
  #   JSON.parse(match_data[1])
  #   YoutubeCaptions::Captions.new(info: info, lang: lang).call
  # end

  def fetch_youtube_from_url!
    require 'httparty'

    # Get the video ID
    if url.start_with?("https://www.youtube.com") || url.start_with?("https://m.youtube.com")
      # https://www.youtube.com/watch?v=TeLwpGkWxkI or https://www.youtube.com/watch?v=7-TtBSyC2BA&t=10s
      video_id = url.split("/watch?v=").last.split("&").first
    elsif url.start_with?("https://youtu.be")
      # https://youtu.be/TeLwpGkWxkI or https://youtu.be/J5-yzYAK038?si=ONUUyM-Dhmqv4FPM
      video_id = url.split("/").last.split("?").first
    end

    # Get the title
    begin
      youtube_html = HTTParty.get("https://www.cpttm.org.mo/gs1/fgc.php?url=https://www.youtube.com/watch?v=#{video_id}")
      # <meta name="title" content="I quit my job, then made $70k in 2 months">
      match_data = youtube_html.match(/<meta name="title" content="(.*?)">/)
      if match_data
        self.update(title: match_data[1])
      end
    rescue => e
      self.update(title: "Untitled Youtube")
    end

    # Get the captions
    begin
      # video = YoutubeCaptions::Video.new(id: video_id)
      video = YoutubeCaptions::AlternativeVideo.new(id: video_id)
      captions = video.captions.join('\n')

      if captions.blank?
        self.update(source_content: "Error: No captions found")
        return
      end

      # remove all "content"=> to make it shorter
      captions = captions.gsub('"content"=>', '')

      ai_response = AiModel.ask_ai_by_model_slug("有以下字幕，格式為{'XXX', 'start'=>'x.xx', 'dur'=>'x.xx'}的列表 start 為出現秒數， dur 為字幕時長秒數，請為提供的字幕列表組合按前文後理分組，共分為最多10組，用繁體中文，每組生成的副標題及500字摘要，不用返回前後文字，不用寫'副標題'或'摘要'：#{captions}。", "deepseek/deepseek-chat")
      summary = ai_response[:content].strip

      self.update(source_content: captions, summary: summary, ai_model: "deepseek/deepseek-chat")
    rescue => e
      # No captions found.
      # captions = "Error: #{e.message}"
      self.update(source_content: "Error: #{e.message}")
    end
  end

  def fetch_content_from_url!
    if url.present?

      if source_content.blank?

        # if it is youtube
        if url.start_with?("https://www.youtube.com") or url.start_with?("https://youtu.be")

          self.update(ai_process_status: 1)

        elsif url.start_with?("https://x.com")
          # pass
        elsif url.start_with?("https://www.facebook.com")
          # pass
        else


          require 'uri'
          require 'net/http'

          token = "2d6e97974349b999ce71a6771b26ad73"
          target_url = URI.encode(url)

          diffbot_url = URI("https://api.diffbot.com/v3/analyze?token=#{token}&url=#{target_url}")

          http = Net::HTTP.new(diffbot_url.host, diffbot_url.port)
          http.use_ssl = true

          request = Net::HTTP::Get.new(diffbot_url)
          request["accept"] = 'application/json'

          response = http.request(request)
          result = JSON.parse(response.read_body)

          if result.dig("objects", 0, "text")

            begin
              title = result.dig("objects", 0, "title")
              content = result.dig("objects", 0, "html")

              # remove all iframe and other non-content elements
              content = content.gsub(/<iframe.*?<\/iframe>/, "")
              content = content.gsub(/<script.*?<\/script>/, "")
              content = content.gsub(/<style.*?<\/style>/, "")
              content = content.gsub(/<noscript.*?<\/noscript>/, "")
              content = content.gsub(/<meta.*?<\/meta>/, "")
              content = content.gsub(/<link.*?<\/link>/, "")
            rescue => e
              content = "Error: #{e.message}"
            end

            if title.blank?
              self.update(title: title, source_content: content)
            else
              self.update(source_content: content)
            end
          end
        end
      end
    end
  end

  def generate_title_if_empty!

    if title.blank?
      unless text_content.blank?
        ai_response = AiModel.ask_ai("請為以下內容生成一個標題，不用返回前後文字，只需直接給出標題：#{text_content}")
        content = ai_response[:content].strip

        self.update(title: content)
      end
    end

    if title.blank?
      unless generated_content.blank?
        ai_response = AiModel.ask_ai("請為以下 AI 所生成的內容生成一個標題，不用返回前後文字，只需直接給出標題： Prompt: #{prompt} Generated content: #{generated_content}")
        content = ai_response[:content].strip

        self.update(title: content)
      end
    end

    if title.blank?
      unless source_content.blank?
        ai_response = AiModel.ask_ai("請為以下原文件內容生成一個標題，不用返回前後文字，只需直接給出標題： Prompt: #{prompt} Source content: #{source_content}")
        content = ai_response[:content].strip

        self.update(title: content)
      end
    end

    if title.blank?
      unless summary.blank?
        ai_response = AiModel.ask_ai("請為以下原文件內容生成一個標題，不用返回前後文字，只需直接給出標題： Prompt: #{prompt} Source content: #{summary}")
        content = ai_response[:content].strip

        self.update(title: content)
      end
    end

    if title.blank?
      unless url.blank?
        # if url beings with "https://x.com"
        if url.start_with?("https://x.com")
          # pass
        elsif url.start_with?("https://www.facebook.com")
          # pass
        elsif url.start_with?("https://youtu.be") or url.start_with?("https://www.youtube.com")
          #pass
        else
          ai_response = AiModel.ask_ai("請讀取以下網址中的內容，生成一個標題，不用返回前後文字，只需直接給出標題： Prompt: #{prompt} URL: #{url}")
          content = ai_response[:content].strip

          self.update(title: content)
        end
      end
    end

    if title.blank?
      if attachments.attached?
        if attachments.any?(&:image?)

          auto_ocr_all_attachments!

          content = attachments.map { |a| a.blob.custom_metadata && a.blob.custom_metadata["ocr_content"] }.join
          ai_response = AiModel.ask_ai("請為以下附件生成一個標題，不用返回前後文字，只需直接給出標題：#{content}")
          content = ai_response[:content].strip

          self.update(title: content)
        elsif attachments.any? { |a| a.blob.filename.to_s.end_with?('.pdf') }
          attachment = attachments.find { |a| a.blob.filename.to_s.end_with?('.pdf') }
          self.update(title: attachment.blob.filename.to_s) if attachment
        elsif attachments.any?(&:audio?)
          attachment = attachments.find { |a| a.audio? }
          self.update(title: attachment.blob.filename.to_s) if attachment
        end
      end
    end
  end

  def summarize_if_only_source_text!
    if source_content.present? && text_content.blank? && generated_content.blank? && summary.blank?
      self.update(ai_model: "openrouter/gemini-flash", prompt: "請生成摘要", ai_process_status: 1)
    end
  end

  def redcarpet
    Redcarpet::Markdown.new(Redcarpet::Render::HTML.new(render_options = {hard_wrap: true}), autolink: true,
                                                     tables: true,
                                                     fenced_code_blocks: true,
                                                     strikethrough: true,
                                                     highlight: true,
                                                     footnotes: true,
                                                     space_after_headers: true,
                                                     no_intra_emphasis: true,
                                                     lax_spacing: true
    )
  end

  def process_ai_prompt!

    prompt_text = prompt

    if system_prompt.present?
      prompt_text = "#{system_prompt}\n\n#{prompt}"
    end

    unless prompt_text.blank?
      actual_prompt = "#{title}\n\n#{frontmatters}\n\n#{text_content}\n\n#{source_content}\n\n#{summary}\n\n#{prompt_text}"
      ai_response = AiModel.ask_ai_by_model_slug(actual_prompt, ai_model)
      self.update(generated_content: ai_response[:content])
    end
  end



  def front_matter
    unsafe_loader = ->(string) { YAML.unsafe_load(string) }
    parsed = FrontMatterParser::Parser.new(:md, loader: unsafe_loader).call("---\n#{frontmatters}\n---\n\n")
    parsed.front_matter
  end

  def markdowned_content
    return "" if text_content.nil?
    redcarpet.render(text_content)
  end

  def markdowned_generated_content
    redcarpet.render(generated_content)
  end

  def markdowned_highlighted_text
    redcarpet.render(highlighted_text)
  end

  def is_waiting_for_generated_content?
    ai_model.present? && (prompt.present? || system_prompt.present?) && generated_content.blank?
  end

  def is_waiting_to_fetch_url_content?
    url.present? && source_content.blank? && text_content.blank? && title.blank?
  end

  def is_waiting_for_youtube_process?
    is_waiting_to_fetch_url_content? && (url.start_with?("https://www.youtube.com") || url.start_with?("https://youtu.be") || url.start_with?("https://m.youtube.com/watch?v=") )
  end

  def only_source_content?
    source_content.present? && text_content.blank? && generated_content.blank? && summary.blank? && highlighted_text.blank?
  end

  def title_blank?
    title.blank?
  end

  # TODOs
  def is_todo?
    kind == "todo"
  end
  def is_done?
    front_matter && front_matter["is_done"] && front_matter["is_done"] == true
  end

  def is_atomy_follow_up?
    kind == "atomy follow-up"
  end

  def is_resource?
    kind == "resource"
  end

  def has_resources?
    children.where(kind: "resource").any?
  end

  def self.today
    where("date_at = ?", Time.zone.now.beginning_of_day)
  end

  def self.future
    where("date_at > ?", Time.zone.now.beginning_of_day)
  end

  def self.past
    where("date_at < ?", Time.zone.now.beginning_of_day)
  end

  def self.no_date
    where("date_at IS NULL")
  end

  # People
  def is_people?
    kind == "people"
  end

  def is_atomy_follow_up?
    kind == "atomy follow-up"
  end

  def has_rows?
    children.where(kind: "row").any?
  end

  # Book
  def is_book?
    kind == "book"
  end

  # Journal
  def is_journal?
    kind == "journal"
  end


  def all_content
    "#{title}\n\n#{frontmatters}\n\n#{text_content}\n\n#{source_content}\n\n#{summary}\n\n#{generated_content}"
  end

  def is_youtube?
    url.present? && (url.start_with?("https://www.youtube.com") || url.start_with?("https://m.youtube.com") || url.start_with?("https://youtu.be"))
  end

  def is_bilibili?
    source_content.present? && (source_content.include?("https://b23.tv"))
  end

  def youtube_id
    # Get the video ID
    if url.start_with?("https://www.youtube.com") || url.start_with?("https://m.youtube.com")
      # https://www.youtube.com/watch?v=TeLwpGkWxkI or https://www.youtube.com/watch?v=7-TtBSyC2BA&t=10s
      video_id = url.split("/watch?v=").last.split("&").first
      return video_id
    elsif url.start_with?("https://youtu.be")
      # https://youtu.be/TeLwpGkWxkI or https://youtu.be/J5-yzYAK038?si=ONUUyM-Dhmqv4FPM
      video_id = url.split("/").last.split("?").first
      return video_id
    end

    nil
  end

  # Calendar Events
  def time_begin_at
    front_matter && front_matter["time_begin_at"] || 0
  end

  def time_end_at
    front_matter && front_matter["time_end_at"] || 0
  end

end
