class Project < ApplicationRecord
    has_many :records
    has_many :progresses

    def to_s
        return name unless name.blank?
        id.to_s
    end

    def todos
        records.where(kind: "todo").not_archived.order(date_at: :asc)
    end

    def done_todos
        records.where(kind: "todo").where("frontmatters LIKE ?", "%is_done: true%").order(date_at: :asc)
    end

    def last_progress
        if progresses.count > 0
            progresses.order(id: :desc).first.progress
        else
            0
        end
    end

    def last_objective
        if progresses.count > 0
            progresses.order(id: :desc).first.objective
        else
            objective
        end
    end

    def last_stakeholders
        if progresses.count > 0
            progresses.order(id: :desc).first.stakeholders
        else
            stakeholders
        end
    end

    def last_schedules
        if progresses.count > 0
            progresses.order(id: :desc).first.schedules
        else
            schedules
        end
    end

    def last_budgets
        if progresses.count > 0
            progresses.order(id: :desc).first.budgets
        else
            budgets
        end
    end

    def last_risks
        if progresses.count > 0
            progresses.order(id: :desc).first.risks
        else
            risks
        end
    end

    def last_issues
        if progresses.count > 0
            progresses.order(id: :desc).first.issues
        else
            issues
        end
    end

end
