class DailyStatistic < ApplicationRecord
    def self.create_or_update_for_date!(date_at)
        daily_statistic = DailyStatistic.find_or_create_by!(date_at: date_at)
        daily_statistic.update(
            text_content_total_length: Record.created_until(date_at).sum { |r| r.text_content.to_s.size },
            highlighted_text_total_length: Record.created_until(date_at).sum { |r| r.highlighted_text.to_s.size },
            # post_it_new_count: Tag.where(name: "post-it").records.created_at(@date_at).count,
            # post_it_total: Tag.where(name: "post-it").records.created_until(@date_at).count,
            post_it_new_count: Record.created_at(date_at).where("tagss LIKE '%post-it%'").count,
            post_it_total: Record.created_until(date_at).where("tagss LIKE '%post-it%'").count,
            todo_new_count: Record.todos.created_at(date_at).count,
            todo_update_count: Record.todos.updated_at(date_at).count,
            todo_total: Record.todos.created_until(date_at).count,
            follow_up_count: Record.where(kind: "atomy follow-up").created_until(date_at).count,
            people_count: Record.where(kind: "people").created_until(date_at).count,
            book_count: Record.where(kind: "book").created_until(date_at).count,
            checking_count: DailyChecking.where(date_at: date_at).count,
            atomy_new_count: Record.colored_as_atomy.created_at(date_at).count,
            atomy_total_count: Record.colored_as_atomy.created_until(date_at).count,
            records_new_count: Record.created_at(date_at).count,
            records_total_count: Record.created_until(date_at).count
        )
        daily_statistic
    end
end
