class AiModel < ApplicationRecord
  belongs_to :ai_provider

  # def self.estimate_tokens(content)
  # end

  def to_s
    "#{ai_provider.slug}/#{slug}"
  end

  def self.estimate_cost(input_tokens, output_tokens, provider, model)
    input_cost = input_tokens * model.usd_per_million_input / 1000000
    output_cost = output_tokens * model.usd_per_million_output / 1000000

    return input_cost + output_cost
  end

  def self.ask_ai_by_model_slug(prompt, model_slug, image_url="")

    provider = model_slug.split("/").first
    model = model_slug.split("/").last

    provider = AiProvider.find_by(slug: provider)
    api_base_url = provider.api_base_url
    api_key = provider.api_key

    model = provider.ai_models.find_by(slug: model)
    model_slug = model.api_slug

    temperature = 0.7

    # Has image?
    prompt_content = prompt

    if image_url.present?
      prompt_content = [
            {
                "type": "text",
                "text": prompt
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": image_url
                }
            }
        ]
    end

    # Calling
    @client = OpenAI::Client.new(
      access_token: api_key,
      uri_base: api_base_url
    )

    response = @client.chat(
      parameters: {
        model: model_slug,
        messages: [
          { role: "system", content: "You are a helpful assistant.除非特別註明，否則總以繁體中文回覆。" },
          { role: "user", content: prompt_content }
        ],
        temperature: temperature
      }
    )
    responsed_content = response.dig("choices", 0, "message", "content")
    input_tokens = response.dig("usage", "prompt_tokens")
    output_tokens = response.dig("usage", "completion_tokens")

    cost = estimate_cost(input_tokens, output_tokens, provider, model)

    AiUsage.create(
      prompt: prompt[0..32700],
      responsed_content: responsed_content,
      cost: cost,
      provider: provider.name,
      model: model.name
    )

    return {
      content: responsed_content,
      cost: cost
    }

  end

  def self.ask_ai(prompt)
    ask_ai_by_model_slug(prompt, "openrouter/gemini-flash")
  end
end
