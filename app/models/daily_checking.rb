class DailyChecking < ApplicationRecord
  belongs_to :daily_checking_template

  def self.title_of_the_day(date_at)
    record = title_of_the_day_record(date_at)
    return nil if record.blank?

    record.remark
  end

  def self.title_of_the_day_record(date_at)
    template = DailyCheckingTemplate.find_by(title: "Title of the day")
    return nil if template.blank?

    checking = template.daily_checkings.find_by(date_at: date_at)
    checking
  end
end
