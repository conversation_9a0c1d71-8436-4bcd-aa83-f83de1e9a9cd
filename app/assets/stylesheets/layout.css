* {
    box-sizing: border-box;
}

/***** Layout *****/

.row {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.row > * {
}

.text-center {
    text-align: center;
}


hr {
    border: none;
    height: 1px;
    background: #e2e3e2;
}

.text-content hr,
.summary-content hr,
.source-content hr,
.generated-content hr,
.daily-checking-content hr,
.assistant-chat-message hr {
    background: #cbcbcb;
    width: 30%;
    margin: 2em auto;
}


/**** End Layout ****/



img, video {
    max-width: 100%;
}

body {
    font-family: Inter, Verdana, Geneva, Tahoma, sans-serif;
    /* max-width: 600px; */
}

.container {
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    &.wide {
        max-width: 800px;
    }
    &.wider {
        max-width: 1200px;
    }
}

p {
    margin-top: .8em;
    margin-bottom: .8em;
}

input, textarea {
    font-family: Inter, Verdana, Geneva, Tahoma, sans-serif;
    font-size: 1.3em;
}

select, input, textarea {
    max-width: 100vw;
}



form > div {
    margin-bottom: 1em;
}

input {
    width: 100%;
    padding: .5em;
}

textarea {
    width: 100%;
    min-height: 5em;
    padding: .5em;
}
.text-content textarea {
    min-height: 40vh;
}

footer {
    margin-top: 3em;
}

a {
    text-decoration: none;
    color: #676767;
    /* display: inline-block; */
    border-radius: 999px;
    margin: 0;
    padding: .1em .2em;
    font-size: 0.875em;
}
a:hover {
    /* text-decoration: underline; */
    color: #333;
    background: #efefef;
    padding: .3em .6em;
    margin: -.2em -.4em;
    /* margin: -2px -4px; */
}
a.button {
    background: #c9e6fc;
    color: #305a82;
    padding: .3em .6em;
    margin: -.2em -.4em;

    &.secondary {
        background: #eaeeea;
        color: #676767;
    }
}
a.button:hover {
    background: #b3d9f5;
}

.text-content a,
.summary-content a,
.source-content a,
.generated-content a,
.daily-checking-content a {
    font-size: 1em;
}

textarea {
    padding: .6em 1.2em;
    margin: .3em 0;
    border-radius: 12px;
    border: 1px solid #efefef;
}

form input[type="text"] {
    padding: .6em 1.2em;
    margin: .3em 0;
    border-radius: 999px;
    border: 1px solid #efefef;
}

form input[type="text"]:focus,
textarea:focus {
    outline: none;
    border: 1px solid #ccc;
}

form input[type="submit"],
form button[type="submit"] {
    padding: .3em .6em;
    margin: .3em 0;
    border-radius: 999px;
    background: #c9e6fc;
    color: #305a82;
    border: none;
    cursor: pointer;
    font-size: 1em;
}


dl {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1em;
}

dt {
    font-weight: bold;
}

dt,
dd {
    margin: 0;
}



.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

.tag {
    background: #c9e6fc;
    color: #305a82;
    padding: .3em .6em;
    margin: .3em 0;
    border-radius: 999px;
    display: inline-block;
    font-size: 0.875em;

    a {
        color: black;

        &:hover {
            background: none;
            text-decoration: none;
        }
    }

    small {
        color: gray;
    }

    &:hover {
        background: rgb(205, 249, 241);
    }
}

.tag-count-0 small {
    display: none;
}

.tag-count-1 small {
    color: #666;
}
.tag-count-2 small {
    color: #333;
}
.tag-count-3 small {
    color: black;
}
.tag-count-4 {
    border: 2px solid #ccc;
}
.tag-count-5,
.tag-count-6 {
    border: 2px solid lightsteelblue;
}
.tag-count-7,
.tag-count-8,
.tag-count-9 {
    border: 2px solid steelblue;
}
.tag-count-10,
.tag-count-11,
.tag-count-12,
.tag-count-13,
.tag-count-14,
.tag-count-15,
.tag-count-16,
.tag-count-17,
.tag-count-18,
.tag-count-19,
.tag-count-20 {
    border: 2px solid lightsalmon;
}

.tags-list a {
    font-size: 0.8em;
    color: #999;
}


.records-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5em;
}
.records-grid > div {
    border: 1px solid #ccc;
    padding: 0.5em;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    max-height: 250px;
    min-height: 140px;
}
.records-grid > div:hover {
    border-color: #999;
}

.records-grid .bottom-right {
    position: absolute;
    bottom: 0;
    right: 0;

    font-size: 0.8em;
    z-index: 999;

    a {
        color: #ccc;
        padding-right: 0.2em;
    }
}

.records-grid .bottom-left {
    position: absolute;
    bottom: 0;
    left: 0;

    font-size: 0.8em;
}

.record-card {
    zoom: 0.8;
}

.record-card-url {
    font-size: 0.8em;

    a {
        color: #999;
        padding-left: 0.2em;
        overflow-wrap: break-word;
        word-wrap: break-word;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 2.4em;
        max-width: 100px;
    }
}

.parent-title {
    color: #666;
}

.color-dot {
    display: inline-block;
    width: 0.8em;
    height: 0.8em;
    border-radius: 50%;
}



.user-chat-message {
    background: rgb(217, 231, 248);
    padding: .4em .8em;
    margin: .4em 0;
    border-radius: 12px;
    display: inline-block;
    color: #222;
    font-size: 0.875em;
}

.assistant-chat-message {
    background: rgb(205, 249, 241);
    padding: .4em .8em;
    margin: .4em 0;
    border-radius: 12px;
    display: inline-block;
    color: #222;
    font-size: 0.875em;
}


.show-only-in-mobile {
    /* display: none; */
}
.hide-in-mobile {
    display: none;
}
@media screen and (min-width: 768px) {
    .show-only-in-mobile {
        display: none;
    }
    .hide-in-mobile {
        display: block;
    }
}

nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
nav ul li {
    padding: 0.5em 0;
    /* text-align: center; */
}
nav ul a {
    display: block;
    /* padding: 1em 0; */
}

ul.mobile-nav {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
}

ul.mobile-nav li {
    padding: 0.5em 0;
}


.fixed-top-right {
    position: fixed;
    top: 0;
    right: 0;
}





.markdowned-margin-fix {
    margin-top: -0.5em;
}


/** Book shelf **/
.books-shelf {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));

}
.books-shelf a:hover {
    transform: scale(1.05);
    background: none;
    color: black;
}
.books-shelf .book {
    border: 1px solid black;
    /* overflow: hidden; */
    position: relative;
    height: 180px;
    overflow: hidden;
    border-radius: 3px;
}
.books-shelf .book-cover {
    width: 180px;
    height: 130%;
    position: absolute;
    top: -6%;
    left: -6%;
}
.books-shelf .book-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: top left;
}
.books-shelf .book-info {
    position: absolute;
    bottom: 0;
    right: 0%;
    padding: 1em 0;
    writing-mode: vertical-rl;
    transform: rotate(180deg);
    padding-left: .2em;
    color: black;
}
.books-shelf .book-info:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    /* box-shadow: 0 0 20px rgba(255, 255, 255, 1); */
    filter: blur(10px);
    z-index: -1;
}
.books-shelf .book-info.chinese-title {
    text-orientation: upright;
    transform: rotate(0deg);
    top: 0;
    bottom: initial;
}

.books-shelf h2 {
    font-size: .9em;
    display: inline;
}
.books-shelf .book-children-count {
    font-size: .5em;
    background:rgb(233, 251, 251);
    padding: .4em .4em;
    border-radius: 20px;
    display: inline-block;
    /* width: 20px; */
    /* height: 20px; */

}


.future-date {
    color: green;
}
.past-date {
    color: red;
}



.google-card {
    border-radius: 12px;
    border: 1px solid #efefef;
    padding: 1em;
}
.google-card h2 {
    margin: 0;
}


.minor-top-margin {
    margin-top: 8px;
}

.minor-bottom-margin {
    margin-bottom: 8px;
}



#webpage-preview {
    width: 100%;
    height: 80vh;
    border: 1px solid #e2e3e2;
    border-radius: 12px;
}


.notice-message {
    color: green;
    text-align: center;
}



.follow-up-remark {
    h2 {
        font-size: 1em;
        margin: 0;
    }

    textarea {
        font-size: .8em;
    }
}


h2#ai-chats {
    font-size: 1em;
}


.grid figure {
    margin: 0;
}