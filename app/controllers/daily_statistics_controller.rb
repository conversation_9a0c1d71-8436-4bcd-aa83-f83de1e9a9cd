class DailyStatisticsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_daily_statistic, only: %i[ show edit update destroy ]

  # GET /daily_statistics or /daily_statistics.json
  def index
    @daily_statistics = DailyStatistic.all.order(date_at: :desc)
  end

  # GET /daily_statistics/1 or /daily_statistics/1.json
  def show
  end

  # GET /daily_statistics/new
  def new
    @daily_statistic = DailyStatistic.new
  end

  def calculate_all_dates
    first_date = Record.first.created_at.to_date
    Date.today.downto(first_date).each do |date|
      DailyStatistic.create_or_update_for_date!(date)
    end
    redirect_to daily_statistics_url
  end

  def calculate_for_day
    @date_at = params[:date]
    if params[:date].blank?
      @date_at = Date.today
    else
      @date_at = @date_at.to_date
    end
    @daily_statistic = DailyStatistic.create_or_update_for_date!(@date_at)
    redirect_to daily_statistics_url
  end

  # GET /daily_statistics/1/edit
  def edit
  end

  # POST /daily_statistics or /daily_statistics.json
  def create
    @daily_statistic = DailyStatistic.new(daily_statistic_params)

    respond_to do |format|
      if @daily_statistic.save
        format.html { redirect_to daily_statistics_url, notice: "Daily statistic was successfully created." }
        format.json { render :show, status: :created, location: @daily_statistic }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @daily_statistic.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /daily_statistics/1 or /daily_statistics/1.json
  def update
    respond_to do |format|
      if @daily_statistic.update(daily_statistic_params)
        format.html { redirect_to daily_statistic_url(@daily_statistic), notice: "Daily statistic was successfully updated." }
        format.json { render :show, status: :ok, location: @daily_statistic }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @daily_statistic.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /daily_statistics/1 or /daily_statistics/1.json
  def destroy
    @daily_statistic.destroy

    respond_to do |format|
      format.html { redirect_to daily_statistics_url, notice: "Daily statistic was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_daily_statistic
      @daily_statistic = DailyStatistic.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def daily_statistic_params
      params.require(:daily_statistic).permit(:date_at, :text_content_total_length, :post_it_new_count, :post_it_total, :todo_new_count, :todo_update_count, :todo_total, :follow_up_count, :people_count, :book_count, :checking_count, :atomy_new_count, :atomy_total_count, :records_new_count, :records_total_count)
    end
end
