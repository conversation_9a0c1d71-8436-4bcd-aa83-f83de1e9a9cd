class AttachmentsController < ApplicationController
    before_action :authenticate_user!

    def index
        @limit = (params[:limit] || 10).to_i
        # @blobs = ActiveStorage::Blob.where.not("metadata ? :key", key: "variant_record")

        @total_storage_size = ActiveStorage::Blob.sum(:byte_size)

        if params[:order] == "size"
            @attachments = ActiveStorage::Attachment.where(record: Record.all)
            .left_joins(:blob)
            .order("active_storage_blobs.byte_size DESC")
            .limit(@limit)
        else
            @attachments = ActiveStorage::Attachment.where(record: Record.all).order(id: :desc).limit(@limit)
        end


        # System's storage remaining
        output = `df -h /`

        # Split the output into lines, take the second line (index 1), split by spaces,
        # and get the fourth element (index 3), which is typically 'Avail'
        lines = output.split("\n")
        if lines.length > 1
            parts = lines[1].split(/\s+/) # Split by one or more whitespace characters
            available_space = parts[3]
            @available_space = "Available harddisk space: #{available_space}."
        else
            @available_space = "Could not parse df output."
        end

    end

    def show
        @blob = ActiveStorage::Blob.find_by_key(params[:id])

        # @blob.custom_metadata ||= {}
        # @blob.custom_metadata["Hello"] = "World"
        # @blob.save
    end

    def ocr
        @blob = ActiveStorage::Blob.find_by_key(params[:attachment_id])

        if @blob.image?
            begin
                ai_response = AiModel.ask_ai_by_model_slug("請辨認此圖片內容，如果是文字，這是我的手寫筆記，請翻譯成繁體中文。幫我重寫提供的文字，成為易讀及有條理的繁體中文文章。如果不是文字而是圖片，請返回最適當的描述，不用上下文，只需要直接回覆，以繁體中文撰寫", "openrouter/gemini-flash", url_for(@blob))
                alt = ai_response[:content].strip
                @blob.update(custom_metadata: { ocr_content: alt })
                @blob.save
            rescue => e
                @blob.update(custom_metadata: { ocr_content: "Error: #{e.message}" })
                @blob.save
            end
        end

        redirect_to attachment_path(@blob.key)
    end

    def delete
        @blob = ActiveStorage::Blob.find_by_key(params[:attachment_id])
        @record = @blob.attachments.first.record
        attachment = @blob.attachments.first
        attachment.purge
        redirect_to @record
    end
end