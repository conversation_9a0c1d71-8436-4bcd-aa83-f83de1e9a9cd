class DailyCheckingTemplatesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_daily_checking_template, only: %i[ show edit update destroy ]

  # GET /daily_checking_templates or /daily_checking_templates.json
  def index
    @daily_checking_templates = DailyCheckingTemplate.order(rank: :asc)
  end

  # GET /daily_checking_templates/1 or /daily_checking_templates/1.json
  def show
  end

  # GET /daily_checking_templates/new
  def new
    @daily_checking_template = DailyCheckingTemplate.new
  end

  # GET /daily_checking_templates/1/edit
  def edit
  end

  # POST /daily_checking_templates or /daily_checking_templates.json
  def create
    @daily_checking_template = DailyCheckingTemplate.new(daily_checking_template_params)

    respond_to do |format|
      if @daily_checking_template.save
        format.html { redirect_to daily_checking_template_url(@daily_checking_template), notice: "Daily checking template was successfully created." }
        format.json { render :show, status: :created, location: @daily_checking_template }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @daily_checking_template.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /daily_checking_templates/1 or /daily_checking_templates/1.json
  def update
    respond_to do |format|
      if @daily_checking_template.update(daily_checking_template_params)
        format.html { redirect_to daily_checking_template_url(@daily_checking_template), notice: "Daily checking template was successfully updated." }
        format.json { render :show, status: :ok, location: @daily_checking_template }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @daily_checking_template.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /daily_checking_templates/1 or /daily_checking_templates/1.json
  def destroy
    @daily_checking_template.destroy

    respond_to do |format|
      format.html { redirect_to daily_checking_templates_url, notice: "Daily checking template was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_daily_checking_template
      @daily_checking_template = DailyCheckingTemplate.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def daily_checking_template_params
      params.require(:daily_checking_template).permit(:title, :is_active, :remarks, :rank)
    end
end
