class ChatMessagesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_chat_message, only: %i[ show edit update destroy ]

  # GET /chat_messages or /chat_messages.json
  def index
    @chat_messages = ChatMessage.all.order(record_id: :desc, id: :asc)
  end

  # GET /chat_messages/1 or /chat_messages/1.json
  def show
  end

  # GET /chat_messages/new
  def new
    @chat_message = ChatMessage.new
  end

  # GET /chat_messages/1/edit
  def edit
  end

  # POST /chat_messages or /chat_messages.json
  def create
    @chat_message = ChatMessage.new(chat_message_params)

    if @chat_message.save

      prompt = "#{@chat_message.record.all_content}\n\nAttachments OCR Content:\n#{@chat_message.record.attachments.map { |attachment| attachment.custom_metadata["ocr_raw_content"] }.join("\n\n")}\n\n-------\n\n對話歷史:\n\n#{@chat_message.record.chat_messages.map { |cm| "#{cm.role}: #{cm.content}" }.join("\n\n")}\n\n#{@chat_message.content}"
      @ai_response = AiModel.ask_ai(prompt)
      new_chat_message = ChatMessage.new(record_id: @chat_message.record_id, content: @ai_response[:content], role: "assistant", ai_model_slug: @chat_message.ai_model_slug)
      new_chat_message.save

      redirect_to record_url(@chat_message.record, anchor: "ai-chats")
    else
      redirect_to @chat_message.record, notice: "Chat message failed to create."
    end
  end

  # PATCH/PUT /chat_messages/1 or /chat_messages/1.json
  def update
    respond_to do |format|
      if @chat_message.update(chat_message_params)
        format.html { redirect_to chat_message_url(@chat_message), notice: "Chat message was successfully updated." }
        format.json { render :show, status: :ok, location: @chat_message }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @chat_message.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /chat_messages/1 or /chat_messages/1.json
  def destroy
    @chat_message.destroy

    respond_to do |format|
      format.html { redirect_to chat_messages_url, notice: "Chat message was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_chat_message
      @chat_message = ChatMessage.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def chat_message_params
      params.require(:chat_message).permit(:record_id, :content, :role, :ai_model_slug)
    end
end
