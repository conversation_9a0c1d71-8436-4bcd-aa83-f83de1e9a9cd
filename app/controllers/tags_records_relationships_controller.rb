class TagsRecordsRelationshipsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_tags_records_relationship, only: %i[ show edit update destroy ]

  # GET /tags_records_relationships or /tags_records_relationships.json
  def index
    @tags_records_relationships = TagsRecordsRelationship.all
  end

  # GET /tags_records_relationships/1 or /tags_records_relationships/1.json
  def show
  end

  # GET /tags_records_relationships/new
  def new
    @tags_records_relationship = TagsRecordsRelationship.new
  end

  # GET /tags_records_relationships/1/edit
  def edit
  end

  # POST /tags_records_relationships or /tags_records_relationships.json
  def create
    @tags_records_relationship = TagsRecordsRelationship.new(tags_records_relationship_params)

    respond_to do |format|
      if @tags_records_relationship.save
        format.html { redirect_to tags_records_relationship_url(@tags_records_relationship), notice: "Tags records relationship was successfully created." }
        format.json { render :show, status: :created, location: @tags_records_relationship }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @tags_records_relationship.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /tags_records_relationships/1 or /tags_records_relationships/1.json
  def update
    respond_to do |format|
      if @tags_records_relationship.update(tags_records_relationship_params)
        format.html { redirect_to tags_records_relationship_url(@tags_records_relationship), notice: "Tags records relationship was successfully updated." }
        format.json { render :show, status: :ok, location: @tags_records_relationship }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @tags_records_relationship.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /tags_records_relationships/1 or /tags_records_relationships/1.json
  def destroy
    @tags_records_relationship.destroy

    respond_to do |format|
      format.html { redirect_to tags_records_relationships_url, notice: "Tags records relationship was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_tags_records_relationship
      @tags_records_relationship = TagsRecordsRelationship.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def tags_records_relationship_params
      params.require(:tags_records_relationship).permit(:tag_id, :record_id)
    end
end
