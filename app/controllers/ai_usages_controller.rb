class AiUsagesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_ai_usage, only: %i[ show edit update destroy ]

  # GET /ai_usages or /ai_usages.json
  def index
    @ai_usages = AiUsage.all.order(id: :desc)
  end

  def all
    @ai_usages = AiUsage.all.order(id: :desc)
  end

  # GET /ai_usages/1 or /ai_usages/1.json
  def show
  end

  # GET /ai_usages/new
  def new
    @ai_usage = AiUsage.new
  end

  # GET /ai_usages/1/edit
  def edit
  end

  # POST /ai_usages or /ai_usages.json
  def create
    @ai_usage = AiUsage.new(ai_usage_params)

    respond_to do |format|
      if @ai_usage.save
        format.html { redirect_to ai_usage_url(@ai_usage), notice: "Ai usage was successfully created." }
        format.json { render :show, status: :created, location: @ai_usage }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @ai_usage.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /ai_usages/1 or /ai_usages/1.json
  def update
    respond_to do |format|
      if @ai_usage.update(ai_usage_params)
        format.html { redirect_to ai_usage_url(@ai_usage), notice: "Ai usage was successfully updated." }
        format.json { render :show, status: :ok, location: @ai_usage }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @ai_usage.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /ai_usages/1 or /ai_usages/1.json
  def destroy
    @ai_usage.destroy

    respond_to do |format|
      format.html { redirect_to ai_usages_url, notice: "Ai usage was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_ai_usage
      @ai_usage = AiUsage.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def ai_usage_params
      params.require(:ai_usage).permit(:prompt, :responsed_content, :cost, :provider, :model, :category)
    end
end
