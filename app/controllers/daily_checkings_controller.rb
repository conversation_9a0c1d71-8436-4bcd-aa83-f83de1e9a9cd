class DailyCheckingsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_daily_checking, only: %i[ show edit update destroy ]

  # GET /daily_checkings or /daily_checkings.json
  def index
    @date_at = params[:date] || Date.today

    if DailyChecking.title_of_the_day_record(@date_at).blank?
      template = DailyCheckingTemplate.find_by(title: "Title of the day")
      DailyChecking.create!(daily_checking_template_id: template.id, date_at: @date_at, rank: template.rank)
    end

    @daily_checkings = DailyChecking.where(date_at: @date_at).order(rank: :asc)

    @todos = Record.where(kind: "todo").where(date_at: @date_at).order(title: :asc)
  end

  # GET /daily_checkings/1 or /daily_checkings/1.json
  def show
  end

  def create_today_checkings
    templates = DailyCheckingTemplate.where(is_active: true)
    templates.each do |template|
      DailyChecking.find_or_create_by!(daily_checking_template_id: template.id, date_at: Date.today, rank: template.rank)
    end
    redirect_to daily_checkings_path, notice: "Daily checkings created successfully."
  end

  def mark_done
    @daily_checking = DailyChecking.find(params[:id])
    @daily_checking.update(is_checked: true)
    redirect_to daily_checkings_path(date: @daily_checking.date_at), notice: "Daily checking marked as done."
  end

  def mark_undone
    @daily_checking = DailyChecking.find(params[:id])
    @daily_checking.update(is_checked: false)
    redirect_to daily_checkings_path(date: @daily_checking.date_at), notice: "Daily checking marked as undone."
  end

  # GET /daily_checkings/new
  def new
    @daily_checking = DailyChecking.new
  end

  # GET /daily_checkings/1/edit
  def edit
  end

  # POST /daily_checkings or /daily_checkings.json
  def create
    @daily_checking = DailyChecking.new(daily_checking_params)

    respond_to do |format|
      if @daily_checking.save
        format.html { redirect_to daily_checking_url(@daily_checking), notice: "Daily checking was successfully created." }
        format.json { render :show, status: :created, location: @daily_checking }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @daily_checking.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /daily_checkings/1 or /daily_checkings/1.json
  def update
    respond_to do |format|
      if @daily_checking.update(daily_checking_params)
        if @daily_checking.date_at != Date.today
          format.html { redirect_to daily_checkings_path(date: @daily_checking.date_at), notice: "Daily checking was successfully updated." }
        else
          format.html { redirect_to daily_checkings_path, notice: "Daily checking was successfully updated." }
          format.json { render :show, status: :ok, location: @daily_checking }
        end
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @daily_checking.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /daily_checkings/1 or /daily_checkings/1.json
  def destroy
    @daily_checking.destroy

    respond_to do |format|
      format.html { redirect_to daily_checkings_url, notice: "Daily checking was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_daily_checking
      @daily_checking = DailyChecking.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def daily_checking_params
      params.require(:daily_checking).permit(:daily_checking_template_id, :date_at, :is_checked, :remark, :rank)
    end
end
