class AiProvidersController < ApplicationController
  before_action :authenticate_user!
  before_action :set_ai_provider, only: %i[ show edit update destroy ]

  # GET /ai_providers or /ai_providers.json
  def index
    @ai_providers = AiProvider.all
  end

  # GET /ai_providers/1 or /ai_providers/1.json
  def show
  end

  # GET /ai_providers/new
  def new
    @ai_provider = AiProvider.new
  end

  # GET /ai_providers/1/edit
  def edit
  end

  # POST /ai_providers or /ai_providers.json
  def create
    @ai_provider = AiProvider.new(ai_provider_params)

    respond_to do |format|
      if @ai_provider.save
        format.html { redirect_to ai_provider_url(@ai_provider), notice: "Ai provider was successfully created." }
        format.json { render :show, status: :created, location: @ai_provider }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @ai_provider.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /ai_providers/1 or /ai_providers/1.json
  def update
    respond_to do |format|
      if @ai_provider.update(ai_provider_params)
        format.html { redirect_to ai_provider_url(@ai_provider), notice: "Ai provider was successfully updated." }
        format.json { render :show, status: :ok, location: @ai_provider }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @ai_provider.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /ai_providers/1 or /ai_providers/1.json
  def destroy
    @ai_provider.destroy

    respond_to do |format|
      format.html { redirect_to ai_providers_url, notice: "Ai provider was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_ai_provider
      @ai_provider = AiProvider.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def ai_provider_params
      params.require(:ai_provider).permit(:name, :slug, :api_base_url, :api_key)
    end
end
