class LifeScenariosController < ApplicationController
  before_action :authenticate_user!
  before_action :set_life_scenario, only: %i[ show edit update destroy ]

  # GET /life_scenarios or /life_scenarios.json
  def index
    @life_scenarios = LifeScenario.all.order(written_at: :desc, id: :desc)
  end

  # GET /life_scenarios/1 or /life_scenarios/1.json
  def show
  end

  # GET /life_scenarios/new
  def new
    @life_scenario = LifeScenario.new
    @life_scenario.written_at = Date.current
  end

  # GET /life_scenarios/1/edit
  def edit
  end

  # POST /life_scenarios or /life_scenarios.json
  def create
    @life_scenario = LifeScenario.new(life_scenario_params)

    respond_to do |format|
      if @life_scenario.save
        format.html { redirect_to life_scenario_url(@life_scenario), notice: "Life scenario was successfully created." }
        format.json { render :show, status: :created, location: @life_scenario }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @life_scenario.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /life_scenarios/1 or /life_scenarios/1.json
  def update
    respond_to do |format|
      if @life_scenario.update(life_scenario_params)
        format.html { redirect_to life_scenario_url(@life_scenario), notice: "Life scenario was successfully updated." }
        format.json { render :show, status: :ok, location: @life_scenario }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @life_scenario.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /life_scenarios/1 or /life_scenarios/1.json
  def destroy
    @life_scenario.destroy

    respond_to do |format|
      format.html { redirect_to life_scenarios_url, notice: "Life scenario was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_life_scenario
      @life_scenario = LifeScenario.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def life_scenario_params
      params.require(:life_scenario).permit(:written_at, :preface, :title_1, :score_1, :remark_1, :title_2, :score_2, :remark_2, :title_3, :score_3, :remark_3, :title_4, :score_4, :remark_4, :title_5, :score_5, :remark_5, :title_6, :score_6, :remark_6, :title_7, :score_7, :remark_7, :title_8, :score_8, :remark_8, :postface)
    end
end
