class TagsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_tag, only: %i[ show edit update destroy ]

  # GET /tags or /tags.json
  def index
    @tags = Tag.all
    @tags = @tags.sort_by { |tag| [tag.name] }
  end

  # GET /tags/1 or /tags/1.json
  def show
    tags = @tag.records.order(id: :desc)
    @tag.known_alias_tags.each do |alias_tag|
      tags += alias_tag.records.order(id: :desc)
    end
    if @tag.alias_of_tag_id.present?
      tags += @tag.alias_of_tag.records.order(id: :desc)
      @tag.alias_of_tag.known_alias_tags.each do |alias_tag|
        tags += alias_tag.records.order(id: :desc)
      end
    end
    @records = tags.uniq
  end

  def quick_assign

    if params[:random] == "true"
      @record = Record.where(tagss: "").order("RAND()").first
    else
      @record = Record.where(tagss: "").order(id: :desc).first
    end

    @tags = Tag.all
    @tags = @tags.sort_by { |tag| [tag.name] }
  end

  # GET /tags/new
  def new
    @tag = Tag.new
  end

  # GET /tags/1/edit
  def edit
  end

  # POST /tags or /tags.json
  def create
    @tag = Tag.new(tag_params)

    respond_to do |format|
      if @tag.save
        format.html { redirect_to tag_url(@tag), notice: "Tag was successfully created." }
        format.json { render :show, status: :created, location: @tag }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @tag.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /tags/1 or /tags/1.json
  def update
    respond_to do |format|
      if @tag.update(tag_params)
        format.html { redirect_to tag_url(@tag), notice: "Tag was successfully updated." }
        format.json { render :show, status: :ok, location: @tag }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @tag.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /tags/1 or /tags/1.json
  def destroy
    @tag.destroy

    respond_to do |format|
      format.html { redirect_to tags_url, notice: "Tag was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_tag
      @tag = Tag.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def tag_params
      params.require(:tag).permit(:name, :color, :alias_of_tag_id)
    end
end
