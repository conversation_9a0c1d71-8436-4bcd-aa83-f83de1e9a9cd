class RecordsController < ApplicationController
  before_action :authenticate_user!, except: %i[ quick_create ]
  before_action :set_record, only: %i[ show edit update destroy mark_pinned mark_unpinned mark_nice_to_have mark_not_nice_to_have mark_archived mark_not_archived mark_readed mark_not_readed ]
  skip_before_action :verify_authenticity_token, only: %i[ quick_create ]


  # Listing
  def index
    @show_calendar= false
    @limit = (params[:limit] || 50).to_i
    if params[:archived].present?
      @records = Record.archived.order(id: :desc)
    elsif params[:is_read].present?
      if params[:is_read] == "true"
        @records = Record.is_read.order(id: :desc).limit(@limit)
      else
        @records = Record.is_unread.order(id: :desc).limit(@limit)
      end
    elsif params[:random] == "book_clips"
      # Get records that are either kind=book or children of kind=book records
      book_records = Record.where(kind: "book").pluck(:id)
      @records = Record.where("kind = 'book' OR parent_id IN (?)", book_records)
                     .order("RAND()")
                     .limit(10)
    elsif params[:random].present?
      @records = Record.all.order("RAND()").limit(10)
    elsif params[:query].present?
      if params[:query] == ".pdf"
        # Search for ActiveStorage attachments that are PDF files
        @attachments = ActiveStorage::Attachment
               .joins(:blob) # Join with ActiveStorage::Blob
               .where("active_storage_blobs.filename LIKE ?", "%.pdf")

        @records = @attachments.map(&:record)
      elsif params[:query] == ".audio"
        @attachments = ActiveStorage::Attachment
               .joins(:blob) # Join with ActiveStorage::Blob
               .where("active_storage_blobs.filename LIKE ? OR active_storage_blobs.filename LIKE ?", "%.mp3", "%.wav")
        @records = @attachments.map(&:record)
      elsif params[:query] == ".url"
        @records = Record.where.not(url: [nil, ""]).order(id: :desc).limit(@limit)
      elsif params[:query] == ".frontmatter"
        @records = Record.where.not(frontmatters: [nil, ""]).order(id: :desc).limit(@limit)
      elsif params[:query] == ".resources"
        # Find the parent_id from kind==resource
        resource_ids = Record.where(kind: "resource").pluck(:parent_id)
        # Find the records with id in resource_ids
        @records = Record.where(id: resource_ids).order(id: :desc)
      else

        # By default, only search title

        if params[:search_for].present?
          if params[:search_for] == "url"
            @records = Record.search_for_url(params[:query]).order(id: :desc)
          elsif params[:search_for] == "frontmatter"
            @records = Record.search_for_frontmatter(params[:query]).order(id: :desc)
          elsif params[:search_for] == "title"
            @records = Record.search_for_title(params[:query]).order(id: :desc)
          end
        else
          # Search records
          @records = Record.search_for(params[:query]).order(id: :desc).limit(@limit)

          # Search attachments custom_metadata
          @attachments = []
          ActiveStorage::Attachment.all.each do |attachment|
            if attachment.custom_metadata && attachment.custom_metadata["ocr_content"]
              if attachment.custom_metadata["ocr_content"].include?(params[:query])
                @attachments << attachment
              end
            end
            if attachment.custom_metadata && attachment.custom_metadata["ocr_raw_content"]
              if attachment.custom_metadata["ocr_raw_content"].include?(params[:query])
                @attachments << attachment
              end
            end
          end

          # Search filename
          attachments = ActiveStorage::Attachment
                 .joins(:blob) # Join with ActiveStorage::Blob
                 .where("active_storage_blobs.filename LIKE ?", "%#{params[:query]}%")
          @records2 = attachments.map(&:record)

          # Search chat message
          chat_messages = ChatMessage.where("content LIKE ?", "%#{params[:query]}%")
          @records3 = chat_messages.map(&:record)
        end


      end
    elsif params[:date]
      @records = Record.date_at(params[:date].to_date)
    elsif params[:color]
      @limit = (params[:limit] || 50).to_i
      @records = Record.where(color: params[:color]).order(id: :desc).limit(@limit)
    elsif params[:id]
      @records = Record.where(id: params[:id])
      @records += Record.where(parent_id: params[:id])
    elsif params[:has]
      if params[:has] == "highlight"
        @records = Record.where.not(highlighted_text: [nil, ""]).order(id: :desc).limit(@limit)
      elsif params[:has] == "text"
        @records = Record.where.not(text_content: [nil, ""]).order(id: :desc).limit(@limit)
      elsif params[:has] == "children"
        @children = Record.where.not(parent_id: nil).uniq
        @records = Record.where(id: @children.pluck(:parent_id), kind: [nil, ""]).order(id: :desc).limit(@limit)

      end
    else
      @show_calendar = true
      @limit = (params[:limit] || 50).to_i
      @records = Record.not_archived.order(id: :desc).limit(@limit)
      unless params[:query] # Don't show pinned records if it is a search query (even empty query)
        @pinned_records = Record.not_archived.pinned.order(updated_at: :desc)
      end
    end

    if params[:tags]
      tagss = params[:tags].split(",").map(&:strip)
      tagss.each do |tag_name|
        tag_name = tag_name.downcase.gsub(/\s+/, '_').gsub(/[^\w\+@#%\$\u0026\*!=\-?\u003c\u003e\[\]{}~.]/, '')
        tag = Tag.find_by(name: tag_name)
        unless tag.nil?
          record_ids = tag.records.pluck(:id)
          @records = @records.where(id: record_ids)
        end
      end
    end


    # For calendar
    if @show_calendar
      today = Date.today
      @events = Record.where(kind: "event").where("date_at >= ? AND date_at <= ?", today.beginning_of_month-1.month, today.end_of_month+1.month)
      @todos = Record.where(kind: "todo").where("date_at >= ? AND date_at <= ?", today.beginning_of_month-1.month, today.end_of_month+1.month)
      @journals = Record.where(kind: "journal").where("date_at >= ? AND date_at <= ?", today.beginning_of_month-1.month, today.end_of_month+1.month)
    end

  end

  def todos
    if params[:tag].present?
      @pinned_todos = Record.where(kind: "todo").not_archived.pinned.joins(:tags).where(tags: { name: params[:tag] }).order(id: :desc)
      @today_todos = Record.where(kind: "todo").not_archived.not_pinned.today.joins(:tags).where(tags: { name: params[:tag] }).order(id: :desc)
      @past_todos = Record.where(kind: "todo").not_archived.not_pinned.past.joins(:tags).where(tags: { name: params[:tag] }).order(id: :desc)
      @future_todos = Record.where(kind: "todo").not_archived.not_pinned.future.joins(:tags).where(tags: { name: params[:tag] }).order(id: :desc)
      @other_todos = Record.where(kind: "todo").not_archived.not_pinned.no_date.joins(:tags).where(tags: { name: params[:tag] }).order(id: :desc)
    else
      @pinned_todos = Record.where(kind: "todo").not_archived.pinned.order(title: :asc)
      @today_todos = Record.where(kind: "todo").not_archived.not_pinned.today.order(title: :asc)
      @past_todos = Record.where(kind: "todo").not_archived.not_pinned.past.order(date_at: :asc, title: :asc)
      @future_todos = Record.where(kind: "todo").not_archived.not_pinned.future.order(date_at: :asc)
      @other_todos = Record.where(kind: "todo").not_archived.not_pinned.no_date.order(id: :desc)
    end

    done_todos = Record.where(kind: "todo").where("frontmatters LIKE ?", "%is_done: true%")
    @tags = []
    (@pinned_todos + @today_todos + @past_todos + @future_todos + @other_todos).each do |record|
      unless done_todos.include?(record)
        @tags += record.tags.map(&:name)
      end
    end
    @tags = @tags.uniq
  end

  def todos_ai
    @pinned_todos = Record.where(kind: "todo").not_archived.pinned.order(title: :asc)
    @today_todos = Record.where(kind: "todo").not_archived.not_pinned.today.order(title: :asc)
    @past_todos = Record.where(kind: "todo").not_archived.not_pinned.past.order(date_at: :asc, title: :asc)
    @future_todos = Record.where(kind: "todo").not_archived.not_pinned.future.order(date_at: :asc)
    @other_todos = Record.where(kind: "todo").not_archived.not_pinned.no_date.order(id: :desc)

    @records = Record.where(kind: "todo").not_archived.not_pinned.order(date_at: :desc)
    @done_today = Record.where(kind: "todo").where("frontmatters LIKE ?", "%is_done: true%").where("updated_at >= ?", Time.zone.now.beginning_of_day)

    todos_content = ""

    @pinned_todos.each do |record|
      unless record.is_done? or record.is_archived?
        todos_content += "Pinned: #{record.title}\n\n#{record.date_at}\nTags: #{record.tagss}\n\n#{record.text_content}\n\n---\n\n"
      end
    end

    @today_todos.each do |record|
      unless record.is_done? or record.is_archived?
        todos_content += "Today: #{record.title}\n\n#{record.date_at}\nTags: #{record.tagss}\n\n#{record.text_content}\n\n---\n\n"
      end
    end
    @past_todos.each do |record|
      unless record.is_done? or record.is_archived?
        todos_content += "Past: #{record.title}\n\n#{record.date_at}\nTags: #{record.tagss}\n\n#{record.text_content}\n\n---\n\n"
      end
    end

    @ai_suggestion = Record.ask_ai_about_todos(todos_content, @done_today.count)

  end

  def done_todos
    @done_today = Record.where(kind: "todo").today.where("frontmatters LIKE ?", "%is_done: true%")
    @other_done = Record.where(kind: "todo").where("frontmatters LIKE ?", "%is_done: true%").order(updated_at: :desc)
  end

  def calendar
    if params[:start_date]
      @start_date = params[:start_date].to_date
    else
      @start_date = Date.today
    end

    @events = Record.where(kind: "event").where("date_at >= ? AND date_at <= ?", @start_date.beginning_of_month-1.month, @start_date.end_of_month+1.month)
    @todos = Record.where(kind: "todo").where("date_at >= ? AND date_at <= ?", @start_date.beginning_of_month-1.month, @start_date.end_of_month+1.month)
    @journals = Record.where(kind: "journal").where("date_at >= ? AND date_at <= ?", @start_date.beginning_of_month-1.month, @start_date.end_of_month+1.month)
  end

  def atomy_follow_ups
    @records = Record.where(kind: "atomy follow-up").order(updated_at: :desc)
  end


  def people
    if params[:query].present?
      @records = Record.where(kind: "people").where("title LIKE ? or text_content LIKE ? or frontmatters LIKE ?", "%#{params[:query]}%", "%#{params[:query]}%", "%#{params[:query]}%").order(title: :asc)
    else
      @records = Record.where(kind: "people").order(title: :asc)
    end
  end

  def quotes
    @limit = (params[:limit] || 50).to_i
    @records = Record.where(kind: "quote").order(updated_at: :desc)
  end

  def books
    @limit = (params[:limit] || 50).to_i

    if params[:query].present?
      @records = Record.where(kind: "book").where("title LIKE ? or text_content LIKE ?", "%#{params[:query]}%", "%#{params[:query]}%").order(updated_at: :desc)
    else
      @records = Record.where(kind: "book").order(updated_at: :desc)
    end

    @view = params[:view] || "shelf"  # options: grid, shelf, list
  end

  def journals
    @limit = (params[:limit] || 5000).to_i
    @records = Record.where(kind: "journal").order(date_at: :desc)
  end

  def rows
    @limit = (params[:limit] || 5000).to_i
    @records = Record.where(kind: "row").order(date_at: :desc, id: :desc)
    @parents = Record.where(id: @records.pluck(:parent_id)).order(kind: :asc, title: :asc)
  end

  def resources
    @limit = (params[:limit] || 5000).to_i
    @records = Record.where(kind: "resource").order(date_at: :desc)

    resource_ids = @records.pluck(:parent_id)
    # Find the records with id in resource_ids
    @container_records = Record.where(id: resource_ids).order(id: :desc)
  end

  # Single Record

  # GET /records/1 or /records/1.json
  def show
    @chat_message = @record.chat_messages.new

  end

  def jobs
    @records = Record.where("ai_process_status > 0 and ai_process_status < 3")

    respond_to do |format|
      format.html
      format.json { render json: @records.count }
    end
  end

  def jobs_running
    @records = Record.where(ai_process_status: 1).order("RAND()")
    if @records.count > 0
      @record = @records.first
      if @record.is_waiting_for_youtube_process?
        @record.update(ai_process_status: 2)
        @record.fetch_youtube_from_url!
        @record.generate_title_if_empty!
        @record.update(ai_process_status: 3)
      elsif @record.is_waiting_to_fetch_url_content?
        @record.update(ai_process_status: 2)
        @record.fetch_content_from_url!
        @record.update(ai_process_status: 3)
        @record.summarize_if_only_source_text!
      elsif @record.is_waiting_for_generated_content?
        @record.update(ai_process_status: 2)
        @record.process_ai_prompt!
        @record.generate_title_if_empty!
        @record.update(ai_process_status: 3)
      elsif @record.only_source_content? && @record.title_blank?
        @record.update(ai_process_status: 2)
        @record.generate_title_if_empty!
        @record.update(ai_process_status: 3)
        @record.summarize_if_only_source_text!
      else
        # ?? Not defined yet.
        @record.generate_title_if_empty!
        @record.update(ai_process_status: 3)
      end
    end

  end

  # GET /records/new
  def new
    @record = Record.new
    @record.parent_id = params[:parent_id]
    @record.project_id = params[:project_id]
    @record.kind = params[:kind]
    if params[:date_at]
      @record.date_at = params[:date_at].to_date
    else
      if params[:kind] == "journal"
        @record.date_at = Date.today
      end
    end
  end

  # GET /records/bulk_new
  def bulk_new
    @record = Record.new
    @record.parent_id = params[:parent_id]
    @record.project_id = params[:project_id]
    @record.kind = params[:kind]
  end

  # GET /records/1/edit
  def edit
  end

  def clear_and_refetch_url
    @record = Record.find(params[:record_id])
    @record.update(source_content: "", title: "", ai_process_status: 1)
    redirect_to @record
  end

  # POST /records or /records.json
  def create
    @record = Record.new(record_params)

    respond_to do |format|
      if @record.save
        if @record.ai_model.blank?
          @record.save_or_update_tags!
          @record.auto_move_text_content_to_url_if_only_url!

          # Find if url duplicated
          if @record.url.present?
            url_record = Record.find_by(url: @record.url)
            if url_record
              # @record.frontmatters += "\nis_duplicate: true\n"
              # @record.save
            end
          end

          # 係咪都先 set 左 AI 去 process 一下就冇錯咖啦。
          @record.update(ai_process_status: 1)

          # @record.fetch_content_from_url!
          # @record.generate_title_if_empty!
          # @record.summarize_if_only_source_text!

        else
          if @record.prompt.present?
            @record.update(ai_process_status: 1)
          end
          # @record.process_ai_prompt!
        end

        format.html { redirect_to record_url(@record), notice: "Record was successfully created." }
        format.json { render :show, status: :created, location: @record }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @record.errors, status: :unprocessable_entity }
      end
    end
  end

  # POST /records/bulk_create
  def bulk_create
    created_records = []
    failed_files = []

    if params[:attachments].present?
      params[:attachments].each do |attachment|
        begin
          # Create a new record for each uploaded file
          record = Record.new(bulk_record_params)
          record.attachments.attach(attachment)

          if record.save
            record.save_or_update_tags!
            record.auto_move_text_content_to_url_if_only_url!
            record.update(ai_process_status: 1)
            created_records << record
          else
            failed_files << { filename: attachment.original_filename, errors: record.errors.full_messages }
          end
        rescue => e
          failed_files << { filename: attachment.original_filename, errors: [e.message] }
        end
      end
    end

    respond_to do |format|
      if failed_files.empty?
        format.html { redirect_to records_url, notice: "Successfully created #{created_records.count} records from uploaded files." }
        format.json { render json: { success: true, created_count: created_records.count, records: created_records.map(&:id) } }
      else
        error_message = "Created #{created_records.count} records. Failed to process #{failed_files.count} files: #{failed_files.map { |f| f[:filename] }.join(', ')}"
        format.html { redirect_to bulk_new_records_url, alert: error_message }
        format.json { render json: { success: false, created_count: created_records.count, failed_files: failed_files } }
      end
    end
  end

  def quick_create
    content = params[:message]
    token = params[:token]
    if token != "thomasmakonly"
      render json: { error: "Invalid token" }, status: :unauthorized
      return
    end

    @record = Record.new(text_content: content, ai_process_status: 1)
    @record.save
    @record.auto_move_text_content_to_url_if_only_url!
    render json: { id: @record.id }
  end

  # PATCH/PUT /records/1 or /records/1.json
  def update
    respond_to do |format|
      if @record.update(record_params)
        @record.save_or_update_tags!
        @record.auto_move_text_content_to_url_if_only_url!
        # @record.fetch_content_from_url!
        # @record.generate_title_if_empty!
        # @record.summarize_if_only_source_text!

        # 係咪都先 set 左 AI 去 process 一下就冇錯咖啦。
        @record.update(ai_process_status: 1)


        if @record.is_waiting_for_generated_content?
          # @record.process_ai_prompt!
          @record.update(ai_process_status: 1)
        end

        # if the submit button value is "Quick Assign Tags", redirect back to tags#quick_assign
        if params[:commit] == "Quick Assign Tags"
          redirect_to controller: "tags", action: "quick_assign"
          return
        end


        format.html { redirect_to record_url(@record), notice: "Record was successfully updated." }
        format.json { render :show, status: :ok, location: @record }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @record.errors, status: :unprocessable_entity }
      end
    end
  end

  def mark_pinned
    @record.is_pinned = true
    @record.save
    redirect_to @record
  end

  def mark_unpinned
    @record.is_pinned = false
    @record.save
    redirect_to @record
  end

  def mark_nice_to_have
    @record.is_nice_to_have = true
    @record.save
    redirect_to @record
  end

  def mark_not_nice_to_have
    @record.is_nice_to_have = false
    @record.save
    redirect_to @record
  end

  def mark_archived
    @record.is_archived = true
    @record.save
    redirect_to @record
  end

  def mark_not_archived
    @record.is_archived = false
    @record.save
    redirect_to @record
  end

  def mark_readed
    @record.readed = true
    @record.save
    redirect_to @record
  end

  def mark_not_readed
    @record.readed = false
    @record.save
    redirect_to @record
  end

  def mark_done
    @record = Record.find(params[:record_id])
    @record.frontmatters += "\nis_done: true"

    # Replace is_done: false to ""
    @record.frontmatters.gsub!("is_done: false", "")
    @record.save

    redirect_to @record
  end

  def set_today
    @record = Record.find(params[:record_id])
    @record.date_at = Date.current
    @record.save

    redirect_to @record
  end

  def set_to_date
    @record = Record.find(params[:record_id])
    @record.date_at = params[:date].to_date
    @record.save

    redirect_to @record
  end

  def set_color
    @record = Record.find(params[:record_id])
    @record.color = params[:color]
    @record.save
    redirect_to @record
  end

  def reset_title
    @record = Record.find(params[:record_id])
    @record.title = ""
    @record.ai_process_status = 1
    @record.save
    redirect_to @record
  end

  def bulk_set_tags
    record_ids = params[:record_ids]
    tagss = params[:tagss]
    tag_names = tagss.split(",").map(&:strip)

    record_ids.split(",").map(&:strip).each do |record_id|
      record = Record.find(record_id.to_i)

      if record.tagss.nil?
        record.tagss = tagss
      else
        record_tag_names = record.tagss.split(",").map(&:strip)
        record_tag_names += tag_names
        record_tag_names.uniq!
        record.tagss = record_tag_names.join(", ")
      end
      record.save_or_update_tags!
      record.save
    end

    redirect_to records_path(query: params[:query])
  end

  # DELETE /records/1 or /records/1.json
  def destroy
    @record.destroy

    respond_to do |format|
      format.html { redirect_to records_url, notice: "Record was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_record
      @record = Record.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def record_params
      params.require(:record).permit(:title, :url, :kind, :text_content, :prompt, :generated_content, :source_content, :summary, :highlighted_text, :frontmatters, :follow_up_remark,
      :ai_model, :parent_id, :ai_process_status, :tagss, :date_at, :system_prompt_id,
      :color, :is_pinned, :is_archived, :is_nice_to_have, :readed, :project_id,
      attachments: [])
    end

    # Parameters for bulk upload - similar to record_params but without requiring :record key
    def bulk_record_params
      params.permit(:title, :url, :kind, :text_content, :prompt, :generated_content, :source_content, :summary, :highlighted_text, :frontmatters, :follow_up_remark,
      :ai_model, :parent_id, :ai_process_status, :tagss, :date_at, :system_prompt_id,
      :color, :is_pinned, :is_archived, :is_nice_to_have, :readed, :project_id)
    end
end
