class ProgressesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_progress, only: %i[ show edit update destroy ]

  # GET /progresses or /progresses.json
  def index
    @project = Project.find(params[:project_id])
    @progresses = @project.progresses.order(id: :desc)
  end

  # GET /progresses/1 or /progresses/1.json
  def show
  end

  # GET /progresses/new
  def new
    @project = Project.find(params[:project_id])
    @progress = @project.progresses.new

    @last_progress = @project.progresses.order(id: :desc).first
    if @last_progress.present?
      @progress.progress = @last_progress.progress

      @progress.objective = @last_progress.objective
      @progress.stakeholders = @last_progress.stakeholders
      @progress.schedules = @last_progress.schedules
      @progress.budgets = @last_progress.budgets
      @progress.risks = @last_progress.risks
      @progress.issues = @last_progress.issues
    else
      @progress.progress = 0

      @progress.objective = @project.objective
      @progress.stakeholders = @project.stakeholders
      @progress.schedules = @project.schedules
      @progress.budgets = @project.budgets
      @progress.risks = @project.risks
      @progress.issues = @project.issues
    end

  end

  # GET /progresses/1/edit
  def edit
  end

  # POST /progresses or /progresses.json
  def create
    @progress = Progress.new(progress_params)

    respond_to do |format|
      if @progress.save
        format.html { redirect_to [@progress.project, :progresses], notice: "Progress was successfully created." }
        format.json { render :show, status: :created, location: @progress }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @progress.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /progresses/1 or /progresses/1.json
  def update
    respond_to do |format|
      if @progress.update(progress_params)
        format.html { redirect_to [@progress.project, @progress], notice: "Progress was successfully updated." }
        format.json { render :show, status: :ok, location: @progress }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @progress.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /progresses/1 or /progresses/1.json
  def destroy
    @progress.destroy

    respond_to do |format|
      format.html { redirect_to [@progress.project, @progress], notice: "Progress was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_progress
      @progress = Progress.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def progress_params
      params.require(:progress).permit(:project_id, :progress, :remark, :objective, :stakeholders, :schedules, :budgets, :risks, :issues)
    end
end
