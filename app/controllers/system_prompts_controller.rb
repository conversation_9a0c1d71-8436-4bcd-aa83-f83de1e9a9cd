class SystemPromptsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_system_prompt, only: %i[ show edit update destroy ]

  # GET /system_prompts or /system_prompts.json
  def index
    @system_prompts = SystemPrompt.all
  end

  # GET /system_prompts/1 or /system_prompts/1.json
  def show
  end

  # GET /system_prompts/new
  def new
    @system_prompt = SystemPrompt.new
  end

  # GET /system_prompts/1/edit
  def edit
  end

  # POST /system_prompts or /system_prompts.json
  def create
    @system_prompt = SystemPrompt.new(system_prompt_params)

    respond_to do |format|
      if @system_prompt.save
        format.html { redirect_to system_prompt_url(@system_prompt), notice: "System prompt was successfully created." }
        format.json { render :show, status: :created, location: @system_prompt }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @system_prompt.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /system_prompts/1 or /system_prompts/1.json
  def update
    respond_to do |format|
      if @system_prompt.update(system_prompt_params)
        format.html { redirect_to system_prompt_url(@system_prompt), notice: "System prompt was successfully updated." }
        format.json { render :show, status: :ok, location: @system_prompt }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @system_prompt.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /system_prompts/1 or /system_prompts/1.json
  def destroy
    @system_prompt.destroy

    respond_to do |format|
      format.html { redirect_to system_prompts_url, notice: "System prompt was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_system_prompt
      @system_prompt = SystemPrompt.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def system_prompt_params
      params.require(:system_prompt).permit(:system_prompt)
    end
end
