# lib/youtube_captions/alternative_info.rb
require 'httparty'

module YoutubeCaptions
  class AlternativeInfo < Info
    def call
      youtube_html = self.class.get("https://www.cpttm.org.mo/gs1/fgc.php?url=https://www.youtube.com/watch?v=#{id}")
      match_data = youtube_html.match(YoutubeCaptions::CAPTIONABLE_REGEX)
      return raise NoCaptionsAvailableError.new("No captions available") unless match_data

      JSON.parse(match_data[1])
    end
  end
end
